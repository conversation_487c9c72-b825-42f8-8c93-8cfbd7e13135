import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api/borudata';

// Test fonksiyonları
async function testAPI() {
    console.log('🚀 Bormegdata API Test Başlıyor...\n');

    try {
        // 1. Veritabanı sağlık kontrolü
        console.log('1️⃣ Veritabanı sağlık kontrolü...');
        const healthResponse = await fetch(`${BASE_URL}/general/health`);
        const healthData = await healthResponse.json();
        console.log('✅ Sağlık kontrolü:', healthData.success ? 'Başarılı' : 'Başarısız');

        // 2. Tabloları listele
        console.log('\n2️⃣ Tablolar listeleniyor...');
        const tablesResponse = await fetch(`${BASE_URL}/general/tables`);
        const tablesData = await tablesResponse.json();
        console.log('✅ Tablolar:', tablesData.data.totalTables, 'tablo bulundu');

        // 3. Veritabanı istatistikleri
        console.log('\n3️⃣ Veritabanı istatistikleri...');
        const statsResponse = await fetch(`${BASE_URL}/general/stats`);
        const statsData = await statsResponse.json();
        console.log('✅ İstatistikler alındı');

        // 4. Ürün modelleri
        console.log('\n4️⃣ Ürün modelleri...');
        const urunResponse = await fetch(`${BASE_URL}/production/urun-modelleri`);
        const urunData = await urunResponse.json();
        console.log('✅ Ürün modelleri:', urunData.data.length, 'ürün');

        // 5. Silodata (Hat 1)
        console.log('\n5️⃣ Silodata Hat 1...');
        const silodataResponse = await fetch(`${BASE_URL}/production/silodata/1?limit=5`);
        const silodataData = await silodataResponse.json();
        console.log('✅ Silodata Hat 1:', silodataData.data.length, 'kayıt');

        // 6. Keçe stok
        console.log('\n6️⃣ Keçe stok...');
        const keceResponse = await fetch(`${BASE_URL}/production/kece-stok`);
        const keceData = await keceResponse.json();
        console.log('✅ Keçe stok:', keceData.data.length, 'kayıt');

        // 7. Ham madde anlık
        console.log('\n7️⃣ Ham madde anlık...');
        const hammaddeResponse = await fetch(`${BASE_URL}/hammadde/anlik?limit=5`);
        const hammaddeData = await hammaddeResponse.json();
        console.log('✅ Ham madde anlık:', hammaddeData.data.length, 'kayıt');

        // 8. Barkod bilgileri
        console.log('\n8️⃣ Barkod bilgileri...');
        const barkodResponse = await fetch(`${BASE_URL}/barcode/bilgileri?limit=5`);
        const barkodData = await barkodResponse.json();
        console.log('✅ Barkod bilgileri:', barkodData.data.length, 'kayıt');

        // 9. Genel arama
        console.log('\n9️⃣ Genel arama...');
        const searchResponse = await fetch(`${BASE_URL}/general/search?q=boru`);
        const searchData = await searchResponse.json();
        console.log('✅ Arama sonuçları:', searchData.data.totalResults, 'sonuç');

        console.log('\n🎉 Tüm API testleri tamamlandı!');

    } catch (error) {
        console.error('❌ Test hatası:', error.message);
    }
}

// Test'i çalıştır
testAPI(); 