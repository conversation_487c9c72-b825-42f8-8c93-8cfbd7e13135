# BORMEGDATA API KULLANIM KILAVUZU

**Base URL:** `http://bormeg.fun:3002`  
**API Version:** 1.1.0  
**Authentication:** <PERSON><PERSON> anda <PERSON>ere<PERSON> (gelecekte aktif edilebilir)

---

## 📋 İÇİNDEKİLER

1. [<PERSON><PERSON> Bilgiler](#genel-bilgiler)
2. [Authentication Endpoints](#authentication-endpoints)
3. [Sistem Endpoints](#sistem-endpoints)
4. [Üretim Endpoints](#üretim-endpoints)
5. [Ham Madde Endpoints](#ham-madde-endpoints)
6. [Barkod Endpoints](#barkod-endpoints)
7. [Genel Endpoints](#genel-endpoints)
8. [Response Formatı](#response-formatı)
9. [<PERSON>rror Handling](#error-handling)

---

## 🏠 GENEL BİLGİLER

### Ana Sayfa
```http
GET http://bormeg.fun:3002/
```

**<PERSON>rnek Response:**
```json
{
  "success": true,
  "message": "Bormegdata API Sunucusu (Authenticated Version)",
  "version": "1.1.0",
  "baseUrl": "http://bormeg.fun:3002/api",
  "authentication": {
    "methods": ["JWT Bearer Token", "API Key"],
    "headers": {
      "jwt": "Authorization: Bearer <token>",
      "apiKey": "X-API-Key: <your-api-key>"
    }
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

---

## 🔐 AUTHENTICATION ENDPOINTS

### Login
```http
POST http://bormeg.fun:3002/api/auth/login
Content-Type: application/json

{
  "username": "bormeg_admin",
  "password": "Bormeg2024!"
}
```

**Örnek Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": "24h",
    "user": {
      "username": "bormeg_admin",
      "permissions": ["read", "write"]
    }
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

### Token Doğrulama
```http
GET http://bormeg.fun:3002/api/auth/verify
Authorization: Bearer <token>
```

**Örnek Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "username": "bormeg_admin",
      "type": "user",
      "permissions": ["read", "write"]
    },
    "authenticated": true
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

---

## 🔧 SİSTEM ENDPOINTS

### Sağlık Kontrolü
```http
GET http://bormeg.fun:3002/api/health
```

**Örnek Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "database": "borudata",
    "host": "*************",
    "timestamp": "2024-01-27T10:30:00.000Z"
  }
}
```

### Tablo Listesi
```http
GET http://bormeg.fun:3002/api/tables
```

**Örnek Response:**
```json
{
  "success": true,
  "data": {
    "database": "borudata",
    "host": "*************",
    "tables": [
      "silodata",
      "silodata2",
      "urun_modelleri",
      "keceStok",
      "HammaddeAnlik"
    ],
    "totalTables": 5
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

### Veritabanı İstatistikleri
```http
GET http://bormeg.fun:3002/api/stats
```

**Örnek Response:**
```json
{
  "success": true,
  "data": {
    "database": "borudata",
    "host": "*************",
    "stats": [
      {
        "table": "silodata",
        "recordCount": 15420
      },
      {
        "table": "urun_modelleri",
        "recordCount": 245
      }
    ],
    "totalTables": 2
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

---

## 🏭 ÜRETİM ENDPOINTS

### Ürün Modelleri

#### Tüm Ürün Modelleri
```http
GET http://bormeg.fun:3002/api/production/urun-modelleri
```

#### Aktif Ürünler
```http
GET http://bormeg.fun:3002/api/production/urun-modelleri?aktif=true
```

#### Stok Kodu ile Filtreleme
```http
GET http://bormeg.fun:3002/api/production/urun-modelleri?stokKodu=BRU001
```

#### Arama
```http
GET http://bormeg.fun:3002/api/production/urun-modelleri?search=boru
```

#### Kombinasyon Filtresi
```http
GET http://bormeg.fun:3002/api/production/urun-modelleri?aktif=true&search=PE&stokKodu=BRU001
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "stok_kodu": "BRU001",
      "stok_adi": "PE100 Boru 50mm",
      "hedef_agirlik": 2.5,
      "uzunluk": 100,
      "aktif": 1
    }
  ],
  "count": 1,
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

### Silodata (Üretim Hatları)

#### Hat 1 Verileri
```http
GET http://bormeg.fun:3002/api/production/silodata/1
```

#### Hat 2 Verileri (Tarih Filtreli)
```http
GET http://bormeg.fun:3002/api/production/silodata/2?startDate=2024-01-01&endDate=2024-01-31
```

#### Hat 3 Verileri (Stok Kodu + Sayfalama)
```http
GET http://bormeg.fun:3002/api/production/silodata/3?stokKodu=BRU001&page=1&limit=50
```

#### Hat 4 Verileri (Durum Filtreli)
```http
GET http://bormeg.fun:3002/api/production/silodata/4?durum=tamamlandi
```

#### Hat 5 Verileri (Sıralama)
```http
GET http://bormeg.fun:3002/api/production/silodata/5?orderBy=tarih&order=ASC
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 12345,
      "stok_kodu": "BRU001",
      "tarih": "2024-01-27T08:00:00.000Z",
      "agirlik": 2.45,
      "durum": "tamamlandi",
      "stok_adi": "PE100 Boru 50mm",
      "hedef_agirlik": 2.5,
      "uzunluk": 100
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 1250,
    "totalPages": 25
  },
  "filters": {
    "hatNumber": "1"
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

### Keçe Stok Yönetimi

#### Tüm Keçe Stok Verileri
```http
GET http://bormeg.fun:3002/api/production/kece-stok
```

#### Tarih Filtreli Keçe Stok
```http
GET http://bormeg.fun:3002/api/production/kece-stok?startDate=2024-01-01&endDate=2024-01-31
```

#### Stok Kodu + Durum Filtreli
```http
GET http://bormeg.fun:3002/api/production/kece-stok?stokKodu=KEC001&durum=aktif
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "ID": 1,
      "stok_kodu": "KEC001",
      "tarih": "2024-01-27T08:00:00.000Z",
      "miktar": 100,
      "durum": "aktif"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 345,
    "totalPages": 7
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

---

## 🧪 HAM MADDE ENDPOINTS

### Ham Madde Anlık Veriler

#### Tüm Anlık Veriler
```http
GET http://bormeg.fun:3002/api/hammadde/anlik
```

#### Tarih Filtreli
```http
GET http://bormeg.fun:3002/api/hammadde/anlik?startDate=2024-01-01&endDate=2024-01-31
```

#### Dozaj Numarası ile Filtreleme
```http
GET http://bormeg.fun:3002/api/hammadde/anlik?dozajNo=1
```

#### Tarih + Dozaj Kombinasyonu
```http
GET http://bormeg.fun:3002/api/hammadde/anlik?startDate=2024-01-15&dozajNo=2
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "ID": 1,
      "Tarih": "2024-01-27T08:00:00.000Z",
      "DozajNo": 1,
      "Miktar": 150.5,
      "Birim": "kg"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 850,
    "totalPages": 17
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

### Ham Madde Set Verileri
```http
GET http://bormeg.fun:3002/api/hammadde/set
```

#### Sayfalama ile
```http
GET http://bormeg.fun:3002/api/hammadde/set?page=2&limit=25
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "ID": 1,
      "SetAdi": "Karışım A",
      "Tarih": "2024-01-27T08:00:00.000Z",
      "ToplamMiktar": 500
    }
  ],
  "pagination": {
    "page": 2,
    "limit": 25,
    "total": 120,
    "totalPages": 5
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

### Ham Madde Stok Verileri
```http
GET http://bormeg.fun:3002/api/hammadde/stok
```

#### Sayfalama ile
```http
GET http://bormeg.fun:3002/api/hammadde/stok?page=1&limit=100
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "ID": 1,
      "MalzemeAdi": "PE Granül",
      "StokMiktari": 1250.75,
      "Birim": "kg",
      "SonGuncellemeTarihi": "2024-01-27T08:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 100,
    "total": 65,
    "totalPages": 1
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

---

## 📊 BARKOD ENDPOINTS

### Barkod Bilgileri

#### Tüm Barkod Bilgileri
```http
GET http://bormeg.fun:3002/api/barcode/bilgileri
```

#### Tarih Filtreli
```http
GET http://bormeg.fun:3002/api/barcode/bilgileri?startDate=2024-01-01&endDate=2024-01-31
```

#### Sayfalama ile
```http
GET http://bormeg.fun:3002/api/barcode/bilgileri?page=1&limit=50
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "barkod": "1234567890123",
      "urun_adi": "PE100 Boru 50mm",
      "tarih": "2024-01-27T08:00:00.000Z",
      "durum": "basili"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 2340,
    "totalPages": 47
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

### Barkod Bilgileri 2
```http
GET http://bormeg.fun:3002/api/barcode/bilgileri2
```

#### Sayfalama ile
```http
GET http://bormeg.fun:3002/api/barcode/bilgileri2?page=2&limit=25
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "barkod": "9876543210987",
      "kategori": "Boru",
      "tarih": "2024-01-27T08:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 2,
    "limit": 25,
    "total": 890,
    "totalPages": 36
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

---

## 🔍 GENEL ENDPOINTS

### Tablo Verisi (Genel)

#### Silodata Tablosu
```http
GET http://bormeg.fun:3002/api/table/silodata?limit=100&offset=0
```

#### Ürün Modelleri Tablosu (Sıralama ile)
```http
GET http://bormeg.fun:3002/api/table/urun_modelleri?orderBy=stok_adi&order=ASC
```

**Örnek Response:**
```json
{
  "success": true,
  "data": {
    "table": "urun_modelleri",
    "results": [
      {
        "id": 1,
        "stok_kodu": "BRU001",
        "stok_adi": "PE100 Boru 50mm",
        "aktif": 1
      }
    ],
    "count": 1
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

**Desteklenen Tablolar:**
- `silodata`, `silodata2`, `silodata3`, `silodata4`, `silodata5`
- `urun_modelleri`
- `keceStok`
- `HammaddeAnlik`, `HammaddeSet`, `HammaddeStok`
- `barkod_bilgileri`, `barkod_bilgileri2`, `barkod_bilgileri3`
- `gunluk_ozet`, `operator_performans`, `user`

### Arama

#### Genel Arama
```http
GET http://bormeg.fun:3002/api/search?q=BRU001
```

#### Belirli Tabloda Arama
```http
GET http://bormeg.fun:3002/api/search?q=boru&table=urun_modelleri
```

#### Belirli Alanlarda Arama
```http
GET http://bormeg.fun:3002/api/search?q=PE&table=urun_modelleri&fields=stok_adi,stok_kodu
```

**Örnek Response:**
```json
{
  "success": true,
  "data": {
    "query": "boru",
    "results": [
      {
        "table": "urun_modelleri",
        "results": [
          {
            "id": 1,
            "stok_kodu": "BRU001",
            "stok_adi": "PE100 Boru 50mm"
          }
        ]
      }
    ],
    "totalResults": 1
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

---

## 📋 RESPONSE FORMATI

### Başarılı Response
```json
{
  "success": true,
  "data": {
    // Veri burada
  },
  "pagination": {  // Sayfalama varsa
    "page": 1,
    "limit": 50,
    "total": 1250,
    "totalPages": 25
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

### Hata Response
```json
{
  "success": false,
  "error": "Hata mesajı",
  "details": "Detaylı hata açıklaması",
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

---

## ⚠️ ERROR HANDLING

### HTTP Status Codes
- **200** - Başarılı
- **400** - Geçersiz istek
- **401** - Yetkisiz erişim
- **403** - Yasak erişim
- **404** - Bulunamadı
- **500** - Sunucu hatası

### Yaygın Hatalar

#### 404 - Endpoint Bulunamadı
```json
{
  "success": false,
  "error": "Endpoint bulunamadı",
  "path": "/api/yanlis-endpoint",
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

#### 400 - Geçersiz Tablo Adı
```json
{
  "success": false,
  "error": "Geçersiz tablo adı",
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

#### 500 - Veritabanı Hatası
```json
{
  "success": false,
  "error": "Veritabanı hatası oluştu",
  "details": "Connection timeout",
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

---

## 📝 NOTLAR

1. **Tarih Formatı:** `YYYY-MM-DD` (örn: `2024-01-27`)
2. **Sayfalama:** `page` (sayfa numarası) ve `limit` (sayfa başına kayıt)
3. **Sıralama:** `orderBy` (alan adı) ve `order` (ASC/DESC)
4. **Filtreleme:** Query parametreleri ile
5. **Authentication:** Şu anda kapalı, gelecekte aktif edilecek

---

## 📞 DESTEK

**Geliştirici:** Bormeg Team  
**API Version:** 1.1.0  
**Son Güncelleme:** 2024-01-27

**Base URL:** `http://bormeg.fun:3002`  
**Health Check:** `http://bormeg.fun:3002/api/health`