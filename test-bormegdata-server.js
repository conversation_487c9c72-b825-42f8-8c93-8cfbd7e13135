const BASE_URL = 'http://localhost:3001/api';

// Test fonksiyonları
async function testBormegdataServer() {
    console.log('🚀 Bormegdata Bağımsız API Sunucusu Test Başlıyor...\n');

    try {
        // 1. Ana sayfa
        console.log('1️⃣ Ana sayfa...');
        const homeResponse = await fetch('http://localhost:3001/');
        const homeData = await homeResponse.json();
        console.log('✅ Ana sayfa:', homeData.message);

        // 2. Veritabanı sağlık kontrolü
        console.log('\n2️⃣ Veritabanı sağlık kontrolü...');
        const healthResponse = await fetch(`${BASE_URL}/health`);
        const healthData = await healthResponse.json();
        console.log('✅ Sağlık kontrolü:', healthData.success ? 'Başarılı' : 'Başarısız');

        // 3. Tabloları listele
        console.log('\n3️⃣ Tablolar listeleniyor...');
        const tablesResponse = await fetch(`${BASE_URL}/tables`);
        const tablesData = await tablesResponse.json();
        console.log('✅ Tablolar:', tablesData.data.totalTables, 'tablo bulundu');

        // 4. Veritabanı istatistikleri
        console.log('\n4️⃣ Veritabanı istatistikleri...');
        const statsResponse = await fetch(`${BASE_URL}/stats`);
        const statsData = await statsResponse.json();
        console.log('✅ İstatistikler alındı');

        // 5. Ürün modelleri
        console.log('\n5️⃣ Ürün modelleri...');
        const urunResponse = await fetch(`${BASE_URL}/production/urun-modelleri`);
        const urunData = await urunResponse.json();
        console.log('✅ Ürün modelleri:', urunData.data.length, 'ürün');

        // 6. Silodata (Hat 1)
        console.log('\n6️⃣ Silodata Hat 1...');
        const silodataResponse = await fetch(`${BASE_URL}/production/silodata/1?limit=5`);
        const silodataData = await silodataResponse.json();
        console.log('✅ Silodata Hat 1:', silodataData.data.length, 'kayıt');

        // 7. Keçe stok
        console.log('\n7️⃣ Keçe stok...');
        const keceResponse = await fetch(`${BASE_URL}/production/kece-stok`);
        const keceData = await keceResponse.json();
        console.log('✅ Keçe stok:', keceData.data.length, 'kayıt');

        // 8. Ham madde anlık
        console.log('\n8️⃣ Ham madde anlık...');
        const hammaddeResponse = await fetch(`${BASE_URL}/hammadde/anlik?limit=5`);
        const hammaddeData = await hammaddeResponse.json();
        console.log('✅ Ham madde anlık:', hammaddeData.data.length, 'kayıt');

        // 9. Barkod bilgileri
        console.log('\n9️⃣ Barkod bilgileri...');
        const barkodResponse = await fetch(`${BASE_URL}/barcode/bilgileri?limit=5`);
        const barkodData = await barkodResponse.json();
        console.log('✅ Barkod bilgileri:', barkodData.data.length, 'kayıt');

        console.log('\n🎉 Tüm API testleri tamamlandı!');
        console.log('\n📊 Özet:');
        console.log(`   - Ana sunucu: ✅ Çalışıyor`);
        console.log(`   - Veritabanı: ✅ Bağlantı var`);
        console.log(`   - Tablolar: ✅ ${tablesData.data.totalTables} tablo`);
        console.log(`   - Üretim API: ✅ Çalışıyor`);
        console.log(`   - Ham madde API: ✅ Çalışıyor`);
        console.log(`   - Barkod API: ✅ Çalışıyor`);
        console.log(`   - Genel API: ✅ Çalışıyor`);

        console.log('\n🌐 API Endpoint\'leri:');
        console.log(`   - Ana sayfa: http://localhost:3001/`);
        console.log(`   - Sağlık kontrolü: http://localhost:3001/api/health`);
        console.log(`   - Tablolar: http://localhost:3001/api/tables`);
        console.log(`   - İstatistikler: http://localhost:3001/api/stats`);
        console.log(`   - Ürün modelleri: http://localhost:3001/api/production/urun-modelleri`);
        console.log(`   - Silodata Hat 1: http://localhost:3001/api/production/silodata/1`);
        console.log(`   - Keçe stok: http://localhost:3001/api/production/kece-stok`);
        console.log(`   - Ham madde anlık: http://localhost:3001/api/hammadde/anlik`);
        console.log(`   - Barkod bilgileri: http://localhost:3001/api/barcode/bilgileri`);

    } catch (error) {
        console.error('❌ Test hatası:', error.message);
        console.log('\n💡 Öneriler:');
        console.log('   - Sunucunun çalıştığından emin olun: npm run api-minimal');
        console.log('   - Port 3001\'in kullanılabilir olduğunu kontrol edin');
        console.log('   - Veritabanı bağlantısını kontrol edin');
    }
}

// Test'i çalıştır
testBormegdataServer(); 