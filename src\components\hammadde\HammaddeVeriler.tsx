import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  TestTube, Shield, GitBranch, Droplets, Atom, Package2, Cog,
  TrendingUp, TrendingDown, Minus, WifiOff, Download, FileSpreadsheet, CalendarDays
} from 'lucide-react';
import { format } from 'date-fns';
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import { toast } from "sonner";
import TarihFiltreleri from './TarihFiltreleri';

// Ham madde listesi
const hammaddeler = [
  { id: 'STAB1', name: 'STAB1', icon: TestTube, color: 'text-blue-600', bgColor: 'bg-blue-100' },
  { id: 'TP32', name: 'TP32', icon: TestTube, color: 'text-blue-600', bgColor: 'bg-blue-100' },
  { id: 'CPE135A', name: 'CPE135A', icon: Shield, color: 'text-red-600', bgColor: 'bg-red-100' },
  { id: 'PROSES', name: 'PROSES', icon: GitBranch, color: 'text-purple-600', bgColor: 'bg-purple-100' },
  { id: 'K58', name: 'K58', icon: Droplets, color: 'text-cyan-600', bgColor: 'bg-cyan-100' },
  { id: 'KALSIT', name: 'KALSİT', icon: Atom, color: 'text-orange-600', bgColor: 'bg-orange-100' },
  { id: 'K67', name: 'K67', icon: Package2, color: 'text-green-600', bgColor: 'bg-green-100' },
  { id: 'MOT12', name: 'MOT12', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT13', name: 'MOT13', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT14', name: 'MOT14', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'DMA650', name: 'DMA650', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'DNSM', name: 'DNSM', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT17', name: 'MOT17', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT18', name: 'MOT18', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
];

interface HammaddeData {
  id: string;
  anlik: number;
  set: number;
  stok: number;
  gunlukToplam: number;
  birim: string;
}

interface HammaddeAnlikRecord {
  ID: number;
  Tarih: string;
  DozajNo: string;
  STAB1: number | string;
  TP32: number | string;
  CPE135A: number | string;
  PROSES: number | string;
  K58: number | string;
  KALSIT: number | string;
  K67: number | string;
  MOT12: number | string;
  MOT13: number | string;
  MOT14: number | string;
  DMA650: number | string;
  DNSM: number | string;
  MOT17: number | string;
  MOT18: number | string;
}

interface HammaddeSetRecord {
  ID: number;
  Tarih: string;
  DozajNo: string;
  STAB1: number | string;
  TP32: number | string;
  CPE135A: number | string;
  PROSES: number | string;
  K58: number | string;
  KALSIT: number | string;
  K67: number | string;
  MOT12: number | string;
  MOT13: number | string;
  MOT14: number | string;
  DMA650: number | string;
  DNSM: number | string;
  MOT17: number | string;
  MOT18: number | string;
}

interface CombinedHammaddeRecord {
  setRecord: HammaddeSetRecord;
  anlikRecord: HammaddeAnlikRecord;
}

export const HammaddeVeriler = () => {
  const [hammaddeData, setHammaddeData] = useState<HammaddeData[]>([]);
  const [anlikRecords, setAnlikRecords] = useState<HammaddeAnlikRecord[]>([]);
  const [setRecords, setSetRecords] = useState<HammaddeSetRecord[]>([]);
  const [combinedRecords, setCombinedRecords] = useState<CombinedHammaddeRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [connectionError, setConnectionError] = useState(false);
  const [lastAnlikUpdate, setLastAnlikUpdate] = useState<string>('');
  const [lastSetUpdate, setLastSetUpdate] = useState<string>('');

  // Tarih filtresi state'leri
  const [selectedDate, setSelectedDate] = useState<string>(format(new Date(), 'yyyy-MM-dd'));
  const [endDate, setEndDate] = useState<string>(format(new Date(), 'yyyy-MM-dd'));
  const [dateFilterType, setDateFilterType] = useState<'tekGun' | 'aralik'>('tekGun');

  // Gerçek API'den veri çek
  useEffect(() => {
    const fetchHammaddeData = async () => {
      try {
        setLoading(true);
        setConnectionError(false);

        // Paralel olarak tüm ham madde verilerini çek
        const [anlikResponse, setResponse, stokResponse, gunlukToplamResponse, anlikListeResponse, setListeResponse] = await Promise.all([
          fetch('/api/hammadde/anlik'),
          fetch('/api/hammadde/set'),
          fetch('/api/hammadde/stok'),
          fetch('/api/hammadde/gunluk-toplam'),
          fetch('/api/hammadde/anlik-liste'),
          fetch('/api/hammadde/set-liste')
        ]);

        // Response'ların başarılı olup olmadığını kontrol et
        if (!anlikResponse.ok || !setResponse.ok || !stokResponse.ok || !gunlukToplamResponse.ok || !anlikListeResponse.ok || !setListeResponse.ok) {
          throw new Error('API yanıt hatası');
        }

        const anlikData = await anlikResponse.json();
        const setData = await setResponse.json();
        const stokData = await stokResponse.json();
        const gunlukToplamData = await gunlukToplamResponse.json();
        const anlikListeData = await anlikListeResponse.json();
        const setListeData = await setListeResponse.json();

        // Verileri birleştir - tablolar sütun bazlı yapıda (Set değerleri kartlarda gösterilmeyecek)
        const combinedData: HammaddeData[] = hammaddeler.map(h => {
          const anlik = anlikData.length > 0 ? anlikData[0][h.id] || 0 : 0;
          const stok = stokData.length > 0 ? stokData[0][h.id] || 0 : 0;
          const gunlukToplam = gunlukToplamData.length > 0 ? gunlukToplamData[0][h.id] || 0 : 0;

          return {
            id: h.id,
            anlik: parseFloat(anlik),
            set: 0, // Set değerleri kartlarda gösterilmeyecek
            stok: parseFloat(stok),
            gunlukToplam: parseFloat(gunlukToplam),
            birim: 'kg'
          };
        });

        setHammaddeData(combinedData);
        setAnlikRecords(anlikListeData);
        setSetRecords(setListeData);

        // Anlık ve Set verilerini birleştir
        const combined = combineAnlikAndSetRecords(anlikListeData, setListeData);
        setCombinedRecords(combined);

        // En son HammaddeAnlik güncellemesini takip et
        if (anlikListeData.length > 0) {
          const latestRecord = anlikListeData[0];
          const updateKey = `${latestRecord.Tarih}-${latestRecord.DozajNo}`;
          setLastAnlikUpdate(updateKey);
        }

        // En son HammaddeSet güncellemesini takip et
        if (setListeData.length > 0) {
          const latestRecord = setListeData[0];
          const updateKey = `${latestRecord.Tarih}-${latestRecord.DozajNo}`;
          setLastSetUpdate(updateKey);
        }
      } catch (error) {
        console.error('Ham madde verileri çekilemedi:', error);
        setConnectionError(true);
        setHammaddeData([]); // Boş array set et
      } finally {
        setLoading(false);
      }
    };

    fetchHammaddeData();

    // 30 saniyede bir verileri güncelle
    const interval = setInterval(fetchHammaddeData, 30000);

    return () => clearInterval(interval);
  }, []);

  // Anlık ve Set verilerini birleştiren fonksiyon - Aynı güne ait tüm verileri göster
  const combineAnlikAndSetRecords = (anlikData: HammaddeAnlikRecord[], setData: HammaddeSetRecord[]): CombinedHammaddeRecord[] => {
    const combined: CombinedHammaddeRecord[] = [];

    // Tarih filtresini uygula
    let filteredAnlik = [...anlikData];
    let filteredSet = [...setData];

    if (dateFilterType === 'tekGun' && selectedDate) {
      const filterDate = new Date(selectedDate);
      filteredAnlik = anlikData.filter(record => {
        const recordDate = new Date(record.Tarih);
        return recordDate.toDateString() === filterDate.toDateString();
      });
      filteredSet = setData.filter(record => {
        const recordDate = new Date(record.Tarih);
        return recordDate.toDateString() === filterDate.toDateString();
      });
    } else if (dateFilterType === 'aralik' && selectedDate && endDate) {
      const startDate = new Date(selectedDate);
      const finishDate = new Date(endDate);

      filteredAnlik = anlikData.filter(record => {
        const recordDate = new Date(record.Tarih);
        return recordDate >= startDate && recordDate <= finishDate;
      });
      filteredSet = setData.filter(record => {
        const recordDate = new Date(record.Tarih);
        return recordDate >= startDate && recordDate <= finishDate;
      });
    }

    // Tarihe göre sırala (en yeni önce)
    filteredAnlik.sort((a, b) => new Date(b.Tarih).getTime() - new Date(a.Tarih).getTime());
    filteredSet.sort((a, b) => new Date(b.Tarih).getTime() - new Date(a.Tarih).getTime());

    // Aynı tarih ve dozaj numarasına sahip kayıtları eşleştir
    const setMap = new Map<string, HammaddeSetRecord>();
    filteredSet.forEach(setRecord => {
      const key = `${setRecord.Tarih}-${setRecord.DozajNo}`;
      setMap.set(key, setRecord);
    });

    filteredAnlik.forEach(anlikRecord => {
      const key = `${anlikRecord.Tarih}-${anlikRecord.DozajNo}`;
      const setRecord = setMap.get(key);

      if (setRecord) {
        combined.push({
          setRecord,
          anlikRecord
        });
      } else {
        // Eşleşen set kaydı yoksa, boş set kaydı oluştur
        const emptySetRecord: HammaddeSetRecord = {
          ID: 0,
          Tarih: anlikRecord.Tarih,
          DozajNo: anlikRecord.DozajNo,
          STAB1: 0, TP32: 0, CPE135A: 0, PROSES: 0, K58: 0, KALSIT: 0, K67: 0,
          MOT12: 0, MOT13: 0, MOT14: 0, DMA650: 0, DNSM: 0, MOT17: 0, MOT18: 0
        };
        combined.push({
          setRecord: emptySetRecord,
          anlikRecord
        });
      }
    });

    return combined;
  };

  // Tarih filtresi değişikliklerinde verileri yeniden birleştir
  useEffect(() => {
    if (anlikRecords.length > 0 || setRecords.length > 0) {
      const combined = combineAnlikAndSetRecords(anlikRecords, setRecords);
      setCombinedRecords(combined);
    }
  }, [selectedDate, endDate, dateFilterType, anlikRecords, setRecords]);

  // HammaddeAnlik veya HammaddeSet güncellendiğinde HammaddeStok ve GunlukToplam verilerini yeniden çek
  useEffect(() => {
    if ((!lastAnlikUpdate && !lastSetUpdate) || loading) return;

    const updateStokAndGunlukData = async () => {
      try {
        const [stokResponse, gunlukToplamResponse] = await Promise.all([
          fetch('/api/hammadde/stok'),
          fetch('/api/hammadde/gunluk-toplam')
        ]);

        if (!stokResponse.ok || !gunlukToplamResponse.ok) return;

        const stokData = await stokResponse.json();
        const gunlukToplamData = await gunlukToplamResponse.json();

        // Mevcut hammaddeData'yı güncelle - stok ve günlük toplam değerlerini
        setHammaddeData(prevData =>
          prevData.map(item => {
            const newStok = stokData.length > 0 ? stokData[0][item.id] || 0 : 0;
            const newGunlukToplam = gunlukToplamData.length > 0 ? gunlukToplamData[0][item.id] || 0 : 0;
            return {
              ...item,
              stok: parseFloat(newStok),
              gunlukToplam: parseFloat(newGunlukToplam)
            };
          })
        );
      } catch (error) {
        console.error('Stok ve günlük toplam verileri güncellenemedi:', error);
      }
    };

    updateStokAndGunlukData();
  }, [lastAnlikUpdate, lastSetUpdate, loading]);

  const getTrendIcon = (anlik: number, set: number) => {
    const diff = anlik - set;
    if (Math.abs(diff) < 10) return <Minus className="h-3 w-3 text-gray-500" />;
    return diff > 0 ?
      <TrendingUp className="h-3 w-3 text-green-500" /> :
      <TrendingDown className="h-3 w-3 text-red-500" />;
  };

  const getStokDurumu = (stok: number) => {
    if (stok < 1000) return { label: 'Düşük', variant: 'destructive' as const };
    if (stok < 3000) return { label: 'Orta', variant: 'secondary' as const };
    return { label: 'Yüksek', variant: 'default' as const };
  };

  // PDF Export fonksiyonu
  const generatePDF = () => {
    try {
      const doc = new jsPDF();

      // Başlık ekle
      doc.setFontSize(16);
      doc.text("Bormeg Plastik Ham Madde Kullanım Verileri", 14, 15);
      doc.setFontSize(10);

      // Tarih bilgisi
      const now = new Date();
      doc.text(`Rapor Alma Tarihi: ${now.toLocaleDateString('tr-TR')} ${now.toLocaleTimeString('tr-TR')}`, 14, 22);

      // Filtre bilgisi
      if (dateFilterType === 'tekGun') {
        doc.text(`Filtre: ${selectedDate} tarihi`, 14, 28);
      } else {
        doc.text(`Filtre: ${selectedDate} - ${endDate} arası`, 14, 28);
      }

      // Tabloyu oluştur
      const headers = [
        'Tarih', 'Dozaj', 'Tip',
        'STAB1', 'TP32', 'CPE135A', 'PROSES', 'K58', 'KALSIT', 'K67',
        'MOT12', 'MOT13', 'MOT14', 'DMA650', 'DNSM', 'MOT17', 'MOT18', 'Toplam'
      ];

      const rows = combinedRecords.flatMap(combinedRecord => [
        // SET değerleri
        [
          format(new Date(combinedRecord.setRecord.Tarih), 'dd.MM.yyyy HH:mm'),
          combinedRecord.setRecord.DozajNo,
          'SET',
          parseFloat(combinedRecord.setRecord.STAB1 || 0).toFixed(1),
          parseFloat(combinedRecord.setRecord.TP32 || 0).toFixed(1),
          parseFloat(combinedRecord.setRecord.CPE135A || 0).toFixed(1),
          parseFloat(combinedRecord.setRecord.PROSES || 0).toFixed(1),
          parseFloat(combinedRecord.setRecord.K58 || 0).toFixed(1),
          parseFloat(combinedRecord.setRecord.KALSIT || 0).toFixed(1),
          parseFloat(combinedRecord.setRecord.K67 || 0).toFixed(1),
          parseFloat(combinedRecord.setRecord.MOT12 || 0).toFixed(1),
          parseFloat(combinedRecord.setRecord.MOT13 || 0).toFixed(1),
          parseFloat(combinedRecord.setRecord.MOT14 || 0).toFixed(1),
          parseFloat(combinedRecord.setRecord.DMA650 || 0).toFixed(1),
          parseFloat(combinedRecord.setRecord.DNSM || 0).toFixed(1),
          parseFloat(combinedRecord.setRecord.MOT17 || 0).toFixed(1),
          parseFloat(combinedRecord.setRecord.MOT18 || 0).toFixed(1),
          (() => {
            const total = hammaddeler
              .filter(h => h.id !== 'DNSM')
              .reduce((sum, h) => sum + parseFloat(combinedRecord.setRecord[h.id as keyof HammaddeSetRecord] || 0), 0);
            return total.toFixed(1);
          })()
        ],
        // ANLIK değerleri
        [
          format(new Date(combinedRecord.anlikRecord.Tarih), 'dd.MM.yyyy HH:mm'),
          combinedRecord.anlikRecord.DozajNo,
          'ANLIK',
          parseFloat(combinedRecord.anlikRecord.STAB1 || 0).toFixed(1),
          parseFloat(combinedRecord.anlikRecord.TP32 || 0).toFixed(1),
          parseFloat(combinedRecord.anlikRecord.CPE135A || 0).toFixed(1),
          parseFloat(combinedRecord.anlikRecord.PROSES || 0).toFixed(1),
          parseFloat(combinedRecord.anlikRecord.K58 || 0).toFixed(1),
          parseFloat(combinedRecord.anlikRecord.KALSIT || 0).toFixed(1),
          parseFloat(combinedRecord.anlikRecord.K67 || 0).toFixed(1),
          parseFloat(combinedRecord.anlikRecord.MOT12 || 0).toFixed(1),
          parseFloat(combinedRecord.anlikRecord.MOT13 || 0).toFixed(1),
          parseFloat(combinedRecord.anlikRecord.MOT14 || 0).toFixed(1),
          parseFloat(combinedRecord.anlikRecord.DMA650 || 0).toFixed(1),
          parseFloat(combinedRecord.anlikRecord.DNSM || 0).toFixed(1),
          parseFloat(combinedRecord.anlikRecord.MOT17 || 0).toFixed(1),
          parseFloat(combinedRecord.anlikRecord.MOT18 || 0).toFixed(1),
          (() => {
            const total = hammaddeler
              .filter(h => h.id !== 'DNSM')
              .reduce((sum, h) => sum + parseFloat(combinedRecord.anlikRecord[h.id as keyof HammaddeAnlikRecord] || 0), 0);
            return total.toFixed(1);
          })()
        ]
      ]);

      autoTable(doc, {
        head: [headers],
        body: rows,
        startY: 35,
        theme: 'grid',
        styles: {
          font: 'helvetica',
          fontSize: 6,
          overflow: 'linebreak',
          cellPadding: 1,
        },
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [245, 245, 245],
        },
      });

      // PDF'i indir
      const fileName = `Bormeg_Ham_Madde_${format(new Date(), 'yyyy-MM-dd_HH-mm')}.pdf`;
      doc.save(fileName);
      toast.success("PDF başarıyla oluşturuldu");
    } catch (error) {
      console.error("PDF oluşturulurken hata:", error);
      toast.error("PDF oluşturulurken bir hata oluştu");
    }
  };

  // Excel Export fonksiyonu (CSV formatında)
  const generateExcel = () => {
    try {
      const headers = [
        'Tarih', 'Dozaj', 'Tip',
        'STAB1', 'TP32', 'CPE135A', 'PROSES', 'K58', 'KALSIT', 'K67',
        'MOT12', 'MOT13', 'MOT14', 'DMA650', 'DNSM', 'MOT17', 'MOT18', 'Toplam'
      ];

      const csvContent = [
        headers.join(','),
        ...combinedRecords.flatMap(combinedRecord => [
          // SET değerleri
          [
            format(new Date(combinedRecord.setRecord.Tarih), 'dd.MM.yyyy HH:mm'),
            combinedRecord.setRecord.DozajNo,
            'SET',
            parseFloat(combinedRecord.setRecord.STAB1 || 0).toFixed(1),
            parseFloat(combinedRecord.setRecord.TP32 || 0).toFixed(1),
            parseFloat(combinedRecord.setRecord.CPE135A || 0).toFixed(1),
            parseFloat(combinedRecord.setRecord.PROSES || 0).toFixed(1),
            parseFloat(combinedRecord.setRecord.K58 || 0).toFixed(1),
            parseFloat(combinedRecord.setRecord.KALSIT || 0).toFixed(1),
            parseFloat(combinedRecord.setRecord.K67 || 0).toFixed(1),
            parseFloat(combinedRecord.setRecord.MOT12 || 0).toFixed(1),
            parseFloat(combinedRecord.setRecord.MOT13 || 0).toFixed(1),
            parseFloat(combinedRecord.setRecord.MOT14 || 0).toFixed(1),
            parseFloat(combinedRecord.setRecord.DMA650 || 0).toFixed(1),
            parseFloat(combinedRecord.setRecord.DNSM || 0).toFixed(1),
            parseFloat(combinedRecord.setRecord.MOT17 || 0).toFixed(1),
            parseFloat(combinedRecord.setRecord.MOT18 || 0).toFixed(1),
            (() => {
              const total = hammaddeler
                .filter(h => h.id !== 'DNSM')
                .reduce((sum, h) => sum + parseFloat(combinedRecord.setRecord[h.id as keyof HammaddeSetRecord] || 0), 0);
              return total.toFixed(1);
            })()
          ].join(','),
          // ANLIK değerleri
          [
            format(new Date(combinedRecord.anlikRecord.Tarih), 'dd.MM.yyyy HH:mm'),
            combinedRecord.anlikRecord.DozajNo,
            'ANLIK',
            parseFloat(combinedRecord.anlikRecord.STAB1 || 0).toFixed(1),
            parseFloat(combinedRecord.anlikRecord.TP32 || 0).toFixed(1),
            parseFloat(combinedRecord.anlikRecord.CPE135A || 0).toFixed(1),
            parseFloat(combinedRecord.anlikRecord.PROSES || 0).toFixed(1),
            parseFloat(combinedRecord.anlikRecord.K58 || 0).toFixed(1),
            parseFloat(combinedRecord.anlikRecord.KALSIT || 0).toFixed(1),
            parseFloat(combinedRecord.anlikRecord.K67 || 0).toFixed(1),
            parseFloat(combinedRecord.anlikRecord.MOT12 || 0).toFixed(1),
            parseFloat(combinedRecord.anlikRecord.MOT13 || 0).toFixed(1),
            parseFloat(combinedRecord.anlikRecord.MOT14 || 0).toFixed(1),
            parseFloat(combinedRecord.anlikRecord.DMA650 || 0).toFixed(1),
            parseFloat(combinedRecord.anlikRecord.DNSM || 0).toFixed(1),
            parseFloat(combinedRecord.anlikRecord.MOT17 || 0).toFixed(1),
            parseFloat(combinedRecord.anlikRecord.MOT18 || 0).toFixed(1),
            (() => {
              const total = hammaddeler
                .filter(h => h.id !== 'DNSM')
                .reduce((sum, h) => sum + parseFloat(combinedRecord.anlikRecord[h.id as keyof HammaddeAnlikRecord] || 0), 0);
              return total.toFixed(1);
            })()
          ].join(',')
        ])
      ].join('\n');

      // CSV dosyasını indir
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `Bormeg_Ham_Madde_${format(new Date(), 'yyyy-MM-dd_HH-mm')}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success("Excel (CSV) başarıyla oluşturuldu");
    } catch (error) {
      console.error("Excel oluşturulurken hata:", error);
      toast.error("Excel oluşturulurken bir hata oluştu");
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid gap-x-1 gap-y-2 grid-cols-14">
          {Array.from({ length: 14 }).map((_, i) => (
            <Card key={i} className="animate-pulse min-w-0">
              <CardHeader className="pb-1 px-1 pt-1">
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent className="px-1 pb-1">
                <div className="space-y-1">
                  <div className="h-2 bg-gray-200 rounded"></div>
                  <div className="h-2 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Bağlantı hatası durumunda özel görünüm
  if (connectionError) {
    return (
      <div className="space-y-4">
        <div className="grid gap-x-1 gap-y-2 grid-cols-14">
          {hammaddeler.map((hammadde) => (
            <Card key={hammadde.id} className="border-red-200 bg-red-50 min-w-0">
              <CardHeader className="pb-1 px-1 pt-1">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    <div className={`p-0.5 rounded-sm ${hammadde.bgColor} opacity-50`}>
                      <hammadde.icon className={`h-2 w-2 ${hammadde.color}`} />
                    </div>
                    <CardTitle className="text-xs font-medium truncate text-gray-500">{hammadde.name}</CardTitle>
                  </div>
                  <div className="flex-shrink-0">
                    <WifiOff className="h-2 w-2 text-red-500" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-1 px-1 pb-1">
                <div className="flex items-center justify-center py-1">
                  <div className="text-xs text-red-500 text-center">
                    Bağlantı Hatası
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Ham madde kartları */}
      <div className="grid gap-x-1 gap-y-2 grid-cols-14">
        {hammaddeler.map((hammadde) => {
          const data = hammaddeData.find(d => d.id === hammadde.id);

          return (
            <Card key={hammadde.id} className="hover:shadow-md transition-shadow min-w-0">
              <CardHeader className="pb-1 px-1 pt-1">
                <div className="flex items-center justify-center">
                  <div className="flex items-center gap-1">
                    <div className={`p-0.5 rounded-sm ${hammadde.bgColor}`}>
                      <hammadde.icon className={`h-2 w-2 ${hammadde.color}`} />
                    </div>
                    <CardTitle className="text-xs font-medium truncate">{hammadde.name}</CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-1 px-1 pb-1">
                {data ? (
                  <>
                    <div className="text-center">
                      <div>
                        <span className="text-muted-foreground text-xs">STOK:</span>
                        <div className="font-semibold text-xs">
                          {data.stok === 0 ? '' : data.stok.toFixed(0)}
                        </div>
                      </div>
                    </div>
                    <div className="text-center">
                      <div>
                        <span className="text-muted-foreground text-xs">GÜNLÜK:</span>
                        <div className="font-semibold text-purple-600 text-xs">
                          {data.gunlukToplam === 0 ? '' : data.gunlukToplam.toFixed(1)}
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="text-xs text-muted-foreground text-center">
                    Yükleniyor...
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Ham madde Set ve Anlık veriler tablosu */}
      <div className="bg-white rounded-lg border overflow-hidden">
        {/* Başlık ve Export Butonları */}
        <div className="flex justify-between items-center p-3 border-b bg-gray-50">
          <div className="text-lg font-bold text-blue-800">
            Günlük Genel Toplam = {(() => {
              if (combinedRecords.length === 0) return '0.0';
              const total = combinedRecords.reduce((sum, record) => {
                const recordTotal = hammaddeler
                  .filter(h => h.id !== 'DNSM')
                  .reduce((recordSum, h) => recordSum + parseFloat(record.anlikRecord[h.id as keyof HammaddeAnlikRecord] || 0), 0);
                return sum + recordTotal;
              }, 0);
              return total.toFixed(1);
            })()} kg
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={generateExcel} className="h-8">
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              Excel
            </Button>
            <Button variant="outline" size="sm" onClick={generatePDF} className="h-8">
              <Download className="mr-2 h-4 w-4" />
              PDF
            </Button>
          </div>
        </div>

        {/* Tarih Filtreleri */}
        <div className="p-3 border-b bg-gray-50">
          <TarihFiltreleri
            selectedDate={selectedDate}
            endDate={endDate}
            dateFilterType={dateFilterType}
            onDateFilterTypeChange={setDateFilterType}
            onDateChange={setSelectedDate}
            onEndDateChange={setEndDate}
          />
        </div>

        <div className="max-h-96 overflow-y-auto overflow-x-auto">
          <Table className="text-xs">
            <TableHeader className="sticky top-0 bg-white">
              <TableRow>
                <TableHead className="text-xs px-2 py-1 min-w-[80px]">Tarih</TableHead>
                <TableHead className="text-xs px-2 py-1 min-w-[60px]">Dozaj</TableHead>
                <TableHead className="text-xs px-2 py-1 min-w-[40px]">Tip</TableHead>
                {hammaddeler.map((hammadde) => (
                  <TableHead key={hammadde.id} className="text-xs px-1 py-1 min-w-[50px]">
                    <div className="flex items-center gap-0.5 justify-center">
                      <div className={`p-0.5 rounded-sm ${hammadde.bgColor}`}>
                        <hammadde.icon className={`h-2 w-2 ${hammadde.color}`} />
                      </div>
                      <span className="font-medium text-xs">{hammadde.name}</span>
                    </div>
                  </TableHead>
                ))}
                <TableHead className="text-xs px-2 py-1 min-w-[60px] bg-yellow-50">
                  <div className="text-center font-bold text-yellow-800">Toplam</div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {combinedRecords.map((combinedRecord, index) => (
                <React.Fragment key={`combined-${index}`}>
                  {/* SET değerleri - Kırmızı renkte */}
                  <TableRow className="hover:bg-red-50 border-b-0">
                    <TableCell className="text-xs px-2 py-1">
                      {new Date(combinedRecord.setRecord.Tarih).toLocaleDateString('tr-TR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: '2-digit'
                      })} {new Date(combinedRecord.setRecord.Tarih).toLocaleTimeString('tr-TR', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </TableCell>
                    <TableCell className="text-xs px-2 py-1">{combinedRecord.setRecord.DozajNo}</TableCell>
                    <TableCell className="text-xs px-2 py-1">
                      <span className="text-red-600 font-semibold">SET</span>
                    </TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.STAB1 || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.STAB1 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.TP32 || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.TP32 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.CPE135A || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.CPE135A || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.PROSES || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.PROSES || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.K58 || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.K58 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.KALSIT || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.KALSIT || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.K67 || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.K67 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.MOT12 || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.MOT12 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.MOT13 || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.MOT13 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.MOT14 || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.MOT14 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.DMA650 || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.DMA650 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.DNSM || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.DNSM || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.MOT17 || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.MOT17 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-red-600 font-medium">{parseFloat(combinedRecord.setRecord.MOT18 || 0) === 0 ? '' : parseFloat(combinedRecord.setRecord.MOT18 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center bg-yellow-50 font-bold text-red-800">
                      {(() => {
                        const total = hammaddeler
                          .filter(h => h.id !== 'DNSM')
                          .reduce((sum, h) => sum + parseFloat(combinedRecord.setRecord[h.id as keyof HammaddeSetRecord] || 0), 0);
                        return total.toFixed(1);
                      })()}
                    </TableCell>
                  </TableRow>
                  {/* ANLIK değerleri - Mavi renkte */}
                  <TableRow className="hover:bg-blue-50 border-b border-gray-200">
                    <TableCell className="text-xs px-2 py-1">
                      {new Date(combinedRecord.anlikRecord.Tarih).toLocaleDateString('tr-TR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: '2-digit'
                      })} {new Date(combinedRecord.anlikRecord.Tarih).toLocaleTimeString('tr-TR', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </TableCell>
                    <TableCell className="text-xs px-2 py-1">{combinedRecord.anlikRecord.DozajNo}</TableCell>
                    <TableCell className="text-xs px-2 py-1">
                      <span className="text-blue-600 font-semibold">ANLIK</span>
                    </TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.STAB1 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.TP32 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.CPE135A || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.PROSES || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.K58 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.KALSIT || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.K67 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.MOT12 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.MOT13 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.MOT14 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.DMA650 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.DNSM || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.MOT17 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center text-blue-600 font-medium">{parseFloat(combinedRecord.anlikRecord.MOT18 || 0).toFixed(1)}</TableCell>
                    <TableCell className="text-xs px-1 py-1 text-center bg-yellow-50 font-bold text-blue-800">
                      {(() => {
                        const total = hammaddeler
                          .filter(h => h.id !== 'DNSM')
                          .reduce((sum, h) => sum + parseFloat(combinedRecord.anlikRecord[h.id as keyof HammaddeAnlikRecord] || 0), 0);
                        return total.toFixed(1);
                      })()}
                    </TableCell>
                  </TableRow>
                </React.Fragment>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};
