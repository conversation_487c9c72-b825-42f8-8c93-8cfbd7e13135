import { Ke<PERSON>Stok, KeceStokFiltre } from '@/models/KeceStok';

export class KeceStokService {
    private static baseUrl = '/api/kece-stok';

    // Keçe stok verilerini getir
    static async getKeceStokListesi(filtre?: KeceStokFiltre): Promise<KeceStok[]> {
        try {
            const params = new URLSearchParams();

            if (filtre?.stokKodu) {
                params.append('stokKodu', filtre.stokKodu);
            }
            if (filtre?.yeniStokKodu) {
                params.append('yeniStokKodu', filtre.yeniStokKodu);
            }
            if (filtre?.baslangicTarihi) {
                params.append('baslangicTarihi', filtre.baslangicTarihi.toISOString());
            }
            if (filtre?.bitisTarihi) {
                params.append('bitisTarihi', filtre.bitisTarihi.toISOString());
            }
            if (filtre?.durum) {
                params.append('durum', filtre.durum);
            }
            if (filtre?.keceAg) {
                params.append('keceAg', filtre.keceAg);
            }

            const url = `${this.baseUrl}?${params.toString()}`;
            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`API yanıt hatası: ${response.status}`);
            }

            const data = await response.json();
            return data.map((item: any) => ({
                ...item,
                id: item.ID || item.id,
                tarih: new Date(item.tarih),
                donusumTarihi: item.donusum_tarihi ? new Date(item.donusum_tarihi) : undefined,
                barkodYazildi: Boolean(item.barkodYazildi),
                agirlikStabil: Boolean(item.agirlik_stabil)
            }));
        } catch (error) {
            console.error('Keçe stok listesi alınırken hata oluştu:', error);
            throw error;
        }
    }

    // Yeni keçe stok kaydı ekle
    static async ekleKeceStok(keceStok: Omit<KeceStok, 'id'>): Promise<KeceStok> {
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(keceStok),
            });

            if (!response.ok) {
                throw new Error(`API yanıt hatası: ${response.status}`);
            }

            const data = await response.json();
            return {
                ...data,
                id: data.ID || data.id,
                tarih: new Date(data.tarih),
                donusumTarihi: data.donusum_tarihi ? new Date(data.donusum_tarihi) : undefined,
                barkodYazildi: Boolean(data.barkodYazildi),
                agirlikStabil: Boolean(data.agirlik_stabil)
            };
        } catch (error) {
            console.error('Keçe stok eklenirken hata oluştu:', error);
            throw error;
        }
    }

    // Keçe stok kaydını güncelle
    static async guncelleKeceStok(id: number, keceStok: Partial<KeceStok>): Promise<KeceStok> {
        try {
            const response = await fetch(`${this.baseUrl}/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(keceStok),
            });

            if (!response.ok) {
                throw new Error(`API yanıt hatası: ${response.status}`);
            }

            const data = await response.json();
            return {
                ...data,
                id: data.ID || data.id,
                tarih: new Date(data.tarih),
                donusumTarihi: data.donusum_tarihi ? new Date(data.donusum_tarihi) : undefined,
                barkodYazildi: Boolean(data.barkodYazildi),
                agirlikStabil: Boolean(data.agirlik_stabil)
            };
        } catch (error) {
            console.error('Keçe stok güncellenirken hata oluştu:', error);
            throw error;
        }
    }

    // Keçe stok kaydını sil
    static async silKeceStok(id: number): Promise<void> {
        try {
            const response = await fetch(`${this.baseUrl}/${id}`, {
                method: 'DELETE',
            });

            if (!response.ok) {
                throw new Error(`API yanıt hatası: ${response.status}`);
            }
        } catch (error) {
            console.error('Keçe stok silinirken hata oluştu:', error);
            throw error;
        }
    }

    // Keçe stok istatistiklerini getir
    static async getKeceStokIstatistikleri(): Promise<{
        toplamKayit: number;
        stokKoduBazinda: Record<string, number>;
        keceAgBazinda: Record<string, number>;
        sonGuncelleme: Date;
    }> {
        try {
            const response = await fetch(`${this.baseUrl}/istatistikler`);

            if (!response.ok) {
                throw new Error(`API yanıt hatası: ${response.status}`);
            }

            const data = await response.json();
            return {
                ...data,
                sonGuncelleme: new Date(data.sonGuncelleme)
            };
        } catch (error) {
            console.error('Keçe stok istatistikleri alınırken hata oluştu:', error);
            throw error;
        }
    }
} 