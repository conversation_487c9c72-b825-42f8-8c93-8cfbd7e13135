# BORME ECHO VERSE - Proje <PERSON>

## Proje Genel Bilgileri

### Proje <PERSON>ı
**Borme Echo Verse** - Entegre İşletme Yönetim Sistemi

### Geliştirme Bilgileri
- **Proje <PERSON>**: React + TypeScript + Node.js/Express Web Uygulaması
- **Geliştirme Platformu**: Lovable.dev
- **Proje URL**: https://lovable.dev/projects/49c123b3-dd0f-4226-baa2-983b17f7a99f
- **Veritabanı**: MySQL
- **Paket Yöneticisi**: npm/bun
- **Build Aracı**: Vite

### Teknoloji Stack'i
- **Frontend**: React 18.3.1, TypeScript, Vite 5.4.1
- **UI Framework**: Tailwind CSS 3.4.11, shadcn/ui
- **State Management**: Zustand 4.4.7, React Query 5.56.2
- **Routing**: React Router DOM 6.26.2
- **Backend**: Node.js, Express 5.1.0
- **Database**: MySQL 3.14.0
- **Authentication**: Context API tabanlı özel auth sistemi

## Proje Mimarisi

### Klasör Yapısı
```
borme-echo-verse/
├── src/
│   ├── components/          # UI bileşenleri
│   │   ├── admin/          # Yönetici paneli
│   │   ├── auth/           # Kimlik doğrulama
│   │   ├── billing/        # Fatura & finans
│   │   ├── cari/           # Cari hesap
│   │   ├── dashboard/      # Dashboard
│   │   ├── hammadde/       # Ham madde
│   │   ├── inventory/      # Envanter
│   │   ├── layout/         # Genel düzen
│   │   ├── maintenance/    # Bakım
│   │   ├── orders/         # Siparişler
│   │   ├── production/     # Üretim
│   │   ├── reports/        # Raporlar
│   │   ├── teamManagement/ # Ekip yönetimi
│   │   ├── ui/            # UI bileşenleri
│   │   └── vehicles/      # Araçlar
│   ├── contexts/          # React Context
│   ├── hooks/             # Custom hooks
│   ├── lib/               # Utility fonksiyonlar
│   ├── models/            # Veri modelleri
│   ├── pages/             # Sayfa bileşenleri
│   ├── routes/            # Routing yapılandırması
│   ├── server/            # Backend API routes
│   ├── services/          # API servisleri
│   ├── stores/            # Zustand store'ları
│   ├── types/             # TypeScript tipler
│   └── utils/             # Utility fonksiyonlar
├── conf/                  # Nginx/Docker yapılandırmaları
├── dist/                  # Build output
├── logo/                  # Logo dosyaları
├── public/                # Static dosyalar
├── yedek/                 # Backup dosyaları
├── server.js              # Express sunucu
├── senkronize.js          # DB senkronizasyon
├── senkronize2.js         # Multi-table sync
└── senkronize3.js         # Silodata3 sync
```

## Modüller ve Özellikler

### 1. Kimlik Doğrulama (Auth)
- **Çoklu şirket desteği**: Farklı şirketler için ayrı login
- **Rol tabanlı erişim**: Admin, Müdür, Muhasebeci rolleri
- **Modül tabanlı yetkilendirme**: Şirketlere özel modül erişimi
- **Giriş log sistemi**: Tüm girişler kayıt altında

#### Önceden Tanımlı Kullanıcılar:
- **Admin**: Şirket Kodu: `12628313332` / Şifre: `password`
- **Bormeg**: Şirket Kodu: `bormeg` / Şifre: `Bor+admin`
- **Muhasebe**: Şirket Kodu: `muhasebe0102` / Şifre: `Senay071124`

### 2. Üretim Yönetimi (Production)
- **Çoklu üretim hattı**: 5 farklı üretim hattı desteği
- **Silo data yönetimi**: Gerçek zamanlı üretim verisi
- **Ürün modelleri**: Stok kodları ve hedef ağırlıklar
- **Kalite kontrol**: Üretim toleransları
- **Dönüşüm sistemi**: Delikli boru → Keçeli boru

#### Üretim Hatları:
- **Hat 1**: `silodata` tablosu
- **Hat 2**: `silodata2` tablosu  
- **Hat 3**: `silodata3` tablosu
- **Hat 4**: `silodata4` tablosu
- **Hat 5**: `silodata5` tablosu

### 3. Finans Yönetimi (Finance)
- **Cari hesap yönetimi**: Müşteri/tedarikçi takibi
- **Fatura sistemi**: Alış/satış faturaları
- **Nakit akış**: Banka/kasa yönetimi
- **Çek/senet takibi**: Vadeli ödeme araçları
- **Alacak/borç**: Finansal pozisyon
- **Muhasebe entegrasyonu**: Hesap kodları
- **E-devlet bağlantısı**: Resmi işlemler

### 4. Stok ve Envanter
- **Ürün kategorileri**: Organizasyon sistemi
- **Tedarikçi yönetimi**: Tedarikçi bilgileri
- **Stok hareketleri**: Giriş/çıkış takibi
- **Minimum stok uyarıları**: Otomatik bildirimler
- **Barkod sistemi**: Ürün tanımlama
- **Konum takibi**: Depo yönetimi

### 5. Ham Madde Yönetimi
- **Anlık veriler**: Gerçek zamanlı takip
- **Set değerleri**: Hedef parametreler
- **Stok takibi**: Ham madde seviyeleri
- **Günlük toplamlar**: Üretim özeti
- **Otomatik güncelleme**: Sürekli veri akışı

### 6. Bakım Yönetimi
- **Periyodik bakım**: Zamanlanmış işlemler
- **Maliyet takibi**: Bakım giderleri
- **Teknisyen ataması**: İş gücü yönetimi
- **Parça yönetimi**: Yedek parça takibi
- **Hatırlatma sistemi**: Otomatik bildirimler

### 7. Araç Yönetimi
- **Araç kayıtları**: Filo yönetimi
- **Yakıt takibi**: Tüketim analizi
- **Sürücü yönetimi**: Personel ataması
- **Masraf hesaplama**: Operasyonel giderler

### 8. Raporlama Sistemi
- **Dinamik raporlar**: Özelleştirilebilir
- **Zamanlanmış raporlar**: Otomatik gönderim
- **Grafik görselleştirme**: Recharts entegrasyonu
- **Excel export**: Veri dışa aktarımı
- **PDF raporları**: jsPDF entegrasyonu

### 9. Yönetim Paneli (Admin)
- **Şirket yönetimi**: Çoklu şirket desteği
- **Kullanıcı yönetimi**: Yetki kontrolü
- **Modül yönetimi**: Özellik aktivasyonu
- **Sistem izleme**: Performans takibi
- **Veritabanı testi**: Bağlantı kontrolü

## Veritabanı Yapısı

### Ana Tablolar
- **silodata** (1-5): Üretim verisi
- **urun_modelleri**: Ürün tanımları
- **HammaddeAnlik**: Ham madde anlık veriler
- **HammaddeSet**: Ham madde set değerleri
- **HammaddeStok**: Ham madde stok durumu
- **urun_kategorileri**: Ürün kategorileri
- **tedarikci_firmalar**: Tedarikçi bilgileri
- **raporlar**: Rapor tanımları
- **bakim_kayitlari**: Bakım işlemleri
- **bakim_parcalari**: Bakım parçaları
- **bakim_takvimi**: Bakım takvimi

### Veritabanı Bağlantıları
- **Yerel DB**: `localhost` (borudata)
- **Uzak DB**: `*************` (borudata)
- **Senkronizasyon**: Otomatik 30 saniye aralıkla

## API Yapısı

### Üretim API'leri
- `GET /api/urun-modelleri` - Ürün modellerini listele
- `GET /api/getSilodata` - Üretim verilerini getir
- `POST /api/silodata` - Yeni üretim verisi ekle
- `PUT /api/silodata/:id` - Üretim verisini güncelle
- `DELETE /api/silodata/:id` - Üretim verisini sil

### Ham Madde API'leri  
- `GET /api/hammadde/anlik` - Anlık veriler
- `GET /api/hammadde/stok` - Stok durumu
- `POST /api/hammadde/stok-girisi` - Stok girişi

### Genel API'ler
- `GET /api/delikli-boru-kategorileri` - Delikli boru listesi
- `POST /api/silodata_genel/convert/:id` - Ürün dönüşümü
- `GET /api/silodata_genel` - Genel üretim verisi

## Senkronizasyon Sistemi

### senkronize.js
- **Amaç**: `silodata` tablosu senkronizasyonu
- **Kaynak**: localhost (borudata)
- **Hedef**: ***********:1481 (borudata)
- **Periyot**: 30 saniye

### senkronize2.js
- **Amaç**: Çoklu tablo senkronizasyonu
- **Tablolar**: silodata2, silodata3, silodata4, silodata5
- **Paralel işleme**: Promise.allSettled
- **Gelişmiş logging**: Emoji ve formatlanmış çıktı

### senkronize3.js
- **Amaç**: Özel silodata3 senkronizasyonu
- **ES6 modülleri**: Modern JavaScript
- **Hata yönetimi**: Kapsamlı error handling

## Güvenlik Özellikleri

### Kimlik Doğrulama
- **Çoklu şirket desteği**: Şirket kodu bazlı giriş
- **Rol tabanlı erişim**: Farklı yetki seviyeleri
- **Modül bazlı yetkilendirme**: Özellik bazlı erişim kontrolü
- **Oturum yönetimi**: LocalStorage tabanlı

### API Güvenliği
- **CORS yapılandırması**: Güvenli cross-origin istekleri
- **Input validasyonu**: Veri doğrulama
- **SQL injection koruması**: Parametreli sorgular
- **Hata yönetimi**: Güvenli hata mesajları

## Performans Optimizasyonları

### Frontend
- **Code splitting**: Lazy loading
- **Memoization**: React.memo kullanımı
- **Bundle optimizasyonu**: Vite build optimizasyonu
- **Cache busting**: Unique hash'ler

### Backend
- **Connection pooling**: MySQL bağlantı havuzu
- **Query optimizasyonu**: İndeksli sorgular
- **Batch işlemler**: Çoklu veri işleme
- **Gzip sıkıştırma**: Veri aktarım optimizasyonu

## Deployment Yapısı

### Build Süreci
```bash
npm run build        # Production build
npm run build:dev    # Development build
npm run preview      # Preview build
```

### Server Yapılandırması
- **HTTP Server**: Port 3000
- **HTTPS Server**: Port 3002 (SSL sertifikası varsa)
- **Static dosyalar**: dist/ klasörü
- **Proxy**: API istekleri için

### SSL Yapılandırması
- **Sertifika yolu**: ssl/ klasörü
- **Dosyalar**: privkey.pem, cert.pem, chain.pem
- **Otomatik HTTPS**: Sertifika varsa aktif

## Özellikler ve Kullanım

### Çoklu Şirket Desteği
Sistem, farklı şirketlerin aynı platformda çalışmasını sağlar:
- Her şirketin kendine özel modülleri
- Ayrı kullanıcı yetkileri
- Bağımsız veri setleri

### Modüler Yapı
Sistem modüler olarak tasarlanmıştır:
- **Temel modüller**: Finans, Stok, İnsan Kaynakları
- **Opsiyonel modüller**: Üretim, Proje, Satış
- **Özel modüller**: Şirket özel geliştirmeler

### Gerçek Zamanlı Veri
- **Üretim verileri**: Anlık takip
- **Ham madde seviyeleri**: Sürekli güncelleme
- **Stok hareketleri**: Anında yansıma
- **Senkronizasyon**: Otomatik veri paylaşımı

## Gelecek Geliştirmeler

### Planlanan Özellikler
- **Mobil uygulama**: React Native
- **Notification sistemi**: WebSocket entegrasyonu
- **Advanced analytics**: AI/ML entegrasyonu
- **API gateway**: Mikroservis mimarisi

### Sistem Gereksinimleri
- **Minimum RAM**: 4GB
- **Minimum Depolama**: 10GB
- **Veritabanı**: MySQL 8.0+
- **Node.js**: 18.0+

## Sorun Giderme

### Yaygın Sorunlar
1. **Veritabanı bağlantı hatası**: Bağlantı bilgilerini kontrol edin
2. **Build hataları**: npm cache clean --force
3. **Port çakışması**: Farklı port kullanın
4. **SSL sertifika**: Sertifika yolunu kontrol edin

### Log Dosyaları
- **Sunucu logları**: Console output
- **Hata logları**: Error stack traces
- **Senkronizasyon logları**: Sync işlem durumu

## İletişim ve Destek

### Geliştirici Bilgileri
- **Platform**: Lovable.dev
- **Proje Linki**: https://lovable.dev/projects/49c123b3-dd0f-4226-baa2-983b17f7a99f
- **Versiyon Kontrol**: Git tabanlı

### Dokümantasyon
- **API Dokümantasyonu**: `/api` endpoints
- **Component Dokümantasyonu**: Storybook (gelecek)
- **Kullanıcı Kılavuzu**: Ayrı doküman

---

*Bu dokümantasyon, Borme Echo Verse projesinin tam analizi sonucu oluşturulmuştur. Proje sürekli geliştirilmekte olup, güncellemeler bu dokümana yansıtılacaktır.*

**Son Güncelleme**: 14 Temmuz 2025
**Doküman Versiyonu**: 1.0