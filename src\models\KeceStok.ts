export interface KeceStok {
    id: number;
    tarih: Date;
    keceAg?: string;
    stokKodu?: string;
    donusumTarihi?: Date;
    durum: string;
    donusumId?: number;
    barkodYazildi?: boolean;
    aktarim?: string;
    agirlikStabil?: boolean;
}

export interface KeceStokFiltre {
    stokKodu?: string;
    baslangicTarihi?: Date;
    bitisTarihi?: Date;
    durum?: string;
    keceAg?: string;
}

export const KECE_STOK_KODLARI = {
    "KC-STK080": "Ø 80 KEÇE",
    "KC-STK100": "Ø 100 KEÇE",
    "KC-STK125": "Ø 125 KEÇE",
    "KC-STK160": "Ø 160 KEÇE",
    "KC-STK200": "Ø 200 KEÇE"
} as const;

export const KECE_AGIRLIKLARI = [
    "Ø 80",
    "Ø 100",
    "Ø 125",
    "Ø 160",
    "Ø 200"
] as const;

export type KeceStokKodu = keyof typeof KECE_STOK_KODLARI;
export type KeceAgirlik = typeof KECE_AGIRLIKLARI[number]; 