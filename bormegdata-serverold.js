import express from 'express';
import mysql from 'mysql2/promise';
import cors from 'cors';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// CORS ayarları
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true
}));

// JSON body parser
app.use(express.json());

// Borudata veritabanı bağlantı havuzu
const pool = mysql.createPool({
    host: '*************',
    user: 'mehmet',
    password: 'Mb_07112024',
    database: 'borudata',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    timezone: '+03:00'
});

// Utility fonksiyonlar
const buildWhereClause = (filters) => {
    const conditions = [];
    const params = [];

    Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
            conditions.push(`${key} = ?`);
            params.push(filters[key]);
        }
    });

    return {
        where: conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '',
        params
    };
};

const buildDateFilter = (startDate, endDate, dateField = 'tarih') => {
    if (startDate && endDate) {
        return `AND DATE(${dateField}) >= ? AND DATE(${dateField}) <= ?`;
    } else if (startDate) {
        return `AND DATE(${dateField}) >= ?`;
    } else if (endDate) {
        return `AND DATE(${dateField}) <= ?`;
    }
    return '';
};

const buildPagination = (page = 1, limit = 50) => {
    const offset = (page - 1) * limit;
    return { limit: parseInt(limit), offset: parseInt(offset) };
};

// ========================================
// 1. ÜRETİM API'LERİ (PRODUCTION)
// ========================================

// Silodata hatları için genel endpoint
app.get('/api/production/silodata/:hatNumber', async (req, res) => {
    try {
        const { hatNumber } = req.params;
        const { page = 1, limit = 50, startDate, endDate, stokKodu, durum, orderBy = 'id', order = 'DESC' } = req.query;

        const tableName = `silodata${hatNumber === '1' ? '' : hatNumber}`;
        const { limit: limitNum, offset } = buildPagination(page, limit);

        let query = `
            SELECT s.*, um.stok_adi, um.hedef_agirlik, um.uzunluk
            FROM ${tableName} s
            LEFT JOIN urun_modelleri um ON s.stok_kodu = um.stok_kodu
            WHERE 1=1
        `;

        const params = [];

        // Tarih filtresi
        if (startDate || endDate) {
            query += ` ${buildDateFilter(startDate, endDate)}`;
            if (startDate) params.push(startDate);
            if (endDate) params.push(endDate);
        }

        // Diğer filtreler
        if (stokKodu) {
            query += ` AND s.stok_kodu = ?`;
            params.push(stokKodu);
        }

        if (durum) {
            query += ` AND s.durum = ?`;
            params.push(durum);
        }

        query += ` ORDER BY s.${orderBy} ${order} LIMIT ? OFFSET ?`;
        params.push(limitNum, offset);

        const [results] = await pool.query(query, params);

        // Toplam kayıt sayısını al
        let countQuery = `SELECT COUNT(*) as total FROM ${tableName} WHERE 1=1`;
        const countParams = [];

        if (startDate || endDate) {
            countQuery += ` ${buildDateFilter(startDate, endDate)}`;
            if (startDate) countParams.push(startDate);
            if (endDate) countParams.push(endDate);
        }

        if (stokKodu) {
            countQuery += ` AND stok_kodu = ?`;
            countParams.push(stokKodu);
        }

        if (durum) {
            countQuery += ` AND durum = ?`;
            countParams.push(durum);
        }

        const [countResult] = await pool.query(countQuery, countParams);
        const total = countResult[0].total;

        res.json({
            success: true,
            data: results,
            pagination: {
                page: parseInt(page),
                limit: limitNum,
                total,
                totalPages: Math.ceil(total / limitNum)
            },
            filters: {
                hatNumber,
                startDate,
                endDate,
                stokKodu,
                durum
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Silodata hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Veritabanı hatası oluştu',
            details: error.message
        });
    }
});

// Tek silodata kaydı getir
app.get('/api/production/silodata/:hatNumber/:id', async (req, res) => {
    try {
        const { hatNumber, id } = req.params;
        const tableName = `silodata${hatNumber === '1' ? '' : hatNumber}`;

        const query = `
            SELECT s.*, um.stok_adi, um.hedef_agirlik, um.uzunluk
            FROM ${tableName} s
            LEFT JOIN urun_modelleri um ON s.stok_kodu = um.stok_kodu
            WHERE s.id = ?
        `;

        const [results] = await pool.query(query, [id]);

        if (results.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Kayıt bulunamadı'
            });
        }

        res.json({
            success: true,
            data: results[0],
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Tek silodata kaydı hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Veritabanı hatası oluştu'
        });
    }
});

// Yeni silodata kaydı ekle
app.post('/api/production/silodata/:hatNumber', async (req, res) => {
    try {
        const { hatNumber } = req.params;
        const data = req.body;
        const tableName = `silodata${hatNumber === '1' ? '' : hatNumber}`;

        const query = `INSERT INTO ${tableName} SET ?`;
        const [result] = await pool.query(query, [data]);

        res.status(201).json({
            success: true,
            data: { id: result.insertId, ...data },
            message: 'Kayıt başarıyla eklendi',
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Silodata ekleme hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Kayıt eklenirken hata oluştu'
        });
    }
});

// Silodata kaydı güncelle
app.put('/api/production/silodata/:hatNumber/:id', async (req, res) => {
    try {
        const { hatNumber, id } = req.params;
        const data = req.body;
        const tableName = `silodata${hatNumber === '1' ? '' : hatNumber}`;

        const query = `UPDATE ${tableName} SET ? WHERE id = ?`;
        const [result] = await pool.query(query, [data, id]);

        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                error: 'Güncellenecek kayıt bulunamadı'
            });
        }

        res.json({
            success: true,
            data: { id, ...data },
            message: 'Kayıt başarıyla güncellendi',
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Silodata güncelleme hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Kayıt güncellenirken hata oluştu'
        });
    }
});

// Silodata kaydı sil
app.delete('/api/production/silodata/:hatNumber/:id', async (req, res) => {
    try {
        const { hatNumber, id } = req.params;
        const tableName = `silodata${hatNumber === '1' ? '' : hatNumber}`;

        const query = `DELETE FROM ${tableName} WHERE id = ?`;
        const [result] = await pool.query(query, [id]);

        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                error: 'Silinecek kayıt bulunamadı'
            });
        }

        res.json({
            success: true,
            message: 'Kayıt başarıyla silindi',
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Silodata silme hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Kayıt silinirken hata oluştu'
        });
    }
});

// Ürün modelleri
app.get('/api/production/urun-modelleri', async (req, res) => {
    try {
        const { aktif, stokKodu, search } = req.query;

        let query = 'SELECT * FROM urun_modelleri WHERE 1=1';
        const params = [];

        if (aktif !== undefined) {
            query += ' AND aktif = ?';
            params.push(aktif === 'true' ? 1 : 0);
        }

        if (stokKodu) {
            query += ' AND stok_kodu = ?';
            params.push(stokKodu);
        }

        if (search) {
            query += ' AND (stok_adi LIKE ? OR stok_kodu LIKE ?)';
            params.push(`%${search}%`, `%${search}%`);
        }

        query += ' ORDER BY stok_adi ASC';

        const [results] = await pool.query(query, params);

        res.json({
            success: true,
            data: results,
            count: results.length,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Ürün modelleri hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Veritabanı hatası oluştu'
        });
    }
});

// Keçe stok yönetimi
app.get('/api/production/kece-stok', async (req, res) => {
    try {
        const { page = 1, limit = 50, stokKodu, durum, startDate, endDate } = req.query;
        const { limit: limitNum, offset } = buildPagination(page, limit);

        let query = 'SELECT * FROM keceStok WHERE 1=1';
        const params = [];

        if (stokKodu) {
            query += ' AND stok_kodu = ?';
            params.push(stokKodu);
        }

        if (durum) {
            query += ' AND durum = ?';
            params.push(durum);
        }

        if (startDate || endDate) {
            query += ` ${buildDateFilter(startDate, endDate, 'tarih')}`;
            if (startDate) params.push(startDate);
            if (endDate) params.push(endDate);
        }

        query += ' ORDER BY ID DESC LIMIT ? OFFSET ?';
        params.push(limitNum, offset);

        const [results] = await pool.query(query, params);

        // Toplam sayı
        let countQuery = 'SELECT COUNT(*) as total FROM keceStok WHERE 1=1';
        const countParams = [];

        if (stokKodu) {
            countQuery += ' AND stok_kodu = ?';
            countParams.push(stokKodu);
        }

        if (durum) {
            countQuery += ' AND durum = ?';
            countParams.push(durum);
        }

        if (startDate || endDate) {
            countQuery += ` ${buildDateFilter(startDate, endDate, 'tarih')}`;
            if (startDate) countParams.push(startDate);
            if (endDate) countParams.push(endDate);
        }

        const [countResult] = await pool.query(countQuery, countParams);
        const total = countResult[0].total;

        res.json({
            success: true,
            data: results,
            pagination: {
                page: parseInt(page),
                limit: limitNum,
                total,
                totalPages: Math.ceil(total / limitNum)
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Keçe stok hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Veritabanı hatası oluştu'
        });
    }
});

// ========================================
// 2. HAM MADDE API'LERİ (RAW MATERIALS)
// ========================================

// Ham madde anlık veriler
app.get('/api/hammadde/anlik', async (req, res) => {
    try {
        const { page = 1, limit = 50, startDate, endDate, dozajNo } = req.query;
        const { limit: limitNum, offset } = buildPagination(page, limit);

        let query = 'SELECT * FROM HammaddeAnlik WHERE 1=1';
        const params = [];

        if (startDate || endDate) {
            query += ` ${buildDateFilter(startDate, endDate, 'Tarih')}`;
            if (startDate) params.push(startDate);
            if (endDate) params.push(endDate);
        }

        if (dozajNo) {
            query += ' AND DozajNo = ?';
            params.push(dozajNo);
        }

        query += ' ORDER BY ID DESC LIMIT ? OFFSET ?';
        params.push(limitNum, offset);

        const [results] = await pool.query(query, params);

        res.json({
            success: true,
            data: results,
            pagination: {
                page: parseInt(page),
                limit: limitNum,
                total: results.length,
                totalPages: Math.ceil(results.length / limitNum)
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Ham madde anlık hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Veritabanı hatası oluştu'
        });
    }
});

// Ham madde set değerleri
app.get('/api/hammadde/set', async (req, res) => {
    try {
        const { page = 1, limit = 50 } = req.query;
        const { limit: limitNum, offset } = buildPagination(page, limit);

        const query = 'SELECT * FROM HammaddeSet ORDER BY ID DESC LIMIT ? OFFSET ?';
        const [results] = await pool.query(query, [limitNum, offset]);

        res.json({
            success: true,
            data: results,
            pagination: {
                page: parseInt(page),
                limit: limitNum,
                total: results.length,
                totalPages: Math.ceil(results.length / limitNum)
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Ham madde set hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Veritabanı hatası oluştu'
        });
    }
});

// Ham madde stok
app.get('/api/hammadde/stok', async (req, res) => {
    try {
        const { page = 1, limit = 50 } = req.query;
        const { limit: limitNum, offset } = buildPagination(page, limit);

        const query = 'SELECT * FROM HammaddeStok ORDER BY ID DESC LIMIT ? OFFSET ?';
        const [results] = await pool.query(query, [limitNum, offset]);

        res.json({
            success: true,
            data: results,
            pagination: {
                page: parseInt(page),
                limit: limitNum,
                total: results.length,
                totalPages: Math.ceil(results.length / limitNum)
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Ham madde stok hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Veritabanı hatası oluştu'
        });
    }
});

// ========================================
// 3. BARKOD API'LERİ (BARCODE)
// ========================================

// Barkod bilgileri
app.get('/api/barcode/bilgileri', async (req, res) => {
    try {
        const { page = 1, limit = 50, startDate, endDate } = req.query;
        const { limit: limitNum, offset } = buildPagination(page, limit);

        let query = 'SELECT * FROM barkod_bilgileri WHERE 1=1';
        const params = [];

        if (startDate || endDate) {
            query += ` ${buildDateFilter(startDate, endDate)}`;
            if (startDate) params.push(startDate);
            if (endDate) params.push(endDate);
        }

        query += ' ORDER BY id DESC LIMIT ? OFFSET ?';
        params.push(limitNum, offset);

        const [results] = await pool.query(query, params);

        res.json({
            success: true,
            data: results,
            pagination: {
                page: parseInt(page),
                limit: limitNum,
                total: results.length,
                totalPages: Math.ceil(results.length / limitNum)
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Barkod bilgileri hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Veritabanı hatası oluştu'
        });
    }
});

// Barkod bilgileri 2
app.get('/api/barcode/bilgileri2', async (req, res) => {
    try {
        const { page = 1, limit = 50 } = req.query;
        const { limit: limitNum, offset } = buildPagination(page, limit);

        const query = 'SELECT * FROM barkod_bilgileri2 ORDER BY id DESC LIMIT ? OFFSET ?';
        const [results] = await pool.query(query, [limitNum, offset]);

        res.json({
            success: true,
            data: results,
            pagination: {
                page: parseInt(page),
                limit: limitNum,
                total: results.length,
                totalPages: Math.ceil(results.length / limitNum)
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Barkod bilgileri2 hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Veritabanı hatası oluştu'
        });
    }
});

// ========================================
// 4. GENEL API'LER (GENERAL)
// ========================================

// Veritabanı sağlık kontrolü
app.get('/api/health', async (req, res) => {
    try {
        const connection = await pool.getConnection();
        connection.release();

        res.json({
            success: true,
            data: {
                status: 'healthy',
                database: 'borudata',
                host: '*************',
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        console.error('Sağlık kontrolü hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Veritabanı bağlantısı başarısız',
            details: error.message
        });
    }
});

// Tüm tabloları listele
app.get('/api/tables', async (req, res) => {
    try {
        const [results] = await pool.query('SHOW TABLES');
        const tables = results.map(row => Object.values(row)[0]);

        res.json({
            success: true,
            data: {
                database: 'borudata',
                host: '*************',
                tables,
                totalTables: tables.length
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Tablolar listesi hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Veritabanı hatası oluştu'
        });
    }
});

// Veritabanı istatistikleri
app.get('/api/stats', async (req, res) => {
    try {
        const [tablesResult] = await pool.query('SHOW TABLES');
        const tables = tablesResult.map(row => Object.values(row)[0]);

        const stats = [];

        for (const table of tables) {
            try {
                const [countResult] = await pool.query(`SELECT COUNT(*) as count FROM ${table}`);
                const count = countResult[0].count;

                stats.push({
                    table,
                    recordCount: count
                });
            } catch (tableError) {
                stats.push({
                    table,
                    error: tableError.message
                });
            }
        }

        res.json({
            success: true,
            data: {
                database: 'borudata',
                host: '*************',
                stats,
                totalTables: tables.length
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('İstatistik hatası:', error);
        res.status(500).json({
            success: false,
            error: 'İstatistik alınırken hata oluştu'
        });
    }
});

// Belirli bir tablodaki verileri getir
app.get('/api/table/:tableName', async (req, res) => {
    try {
        const { tableName } = req.params;
        const { limit = 100, offset = 0, orderBy = 'id', order = 'DESC' } = req.query;

        // SQL injection koruması
        const allowedTables = [
            'silodata', 'silodata2', 'silodata3', 'silodata4', 'silodata5',
            'urun_modelleri', 'keceStok', 'HammaddeAnlik', 'HammaddeSet', 'HammaddeStok',
            'barkod_bilgileri', 'barkod_bilgileri2', 'barkod_bilgileri3',
            'gunluk_ozet', 'operator_performans', 'user'
        ];

        if (!allowedTables.includes(tableName)) {
            return res.status(400).json({
                success: false,
                error: 'Geçersiz tablo adı'
            });
        }

        const query = `SELECT * FROM ${tableName} ORDER BY ${orderBy} ${order} LIMIT ? OFFSET ?`;
        const [results] = await pool.query(query, [parseInt(limit), parseInt(offset)]);

        res.json({
            success: true,
            data: {
                table: tableName,
                records: results,
                count: results.length,
                limit: parseInt(limit),
                offset: parseInt(offset)
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Tablo verisi hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Veritabanı hatası oluştu'
        });
    }
});

// Genel arama
app.get('/api/search', async (req, res) => {
    try {
        const { q, table, fields } = req.query;

        if (!q) {
            return res.status(400).json({
                success: false,
                error: 'Arama terimi gerekli'
            });
        }

        const allowedTables = [
            'silodata', 'urun_modelleri', 'keceStok', 'HammaddeAnlik'
        ];

        if (table && !allowedTables.includes(table)) {
            return res.status(400).json({
                success: false,
                error: 'Geçersiz tablo adı'
            });
        }

        const searchTables = table ? [table] : allowedTables;
        const searchResults = [];

        for (const searchTable of searchTables) {
            try {
                let searchFields = 'stok_kodu, stok_adi';
                if (fields) {
                    searchFields = fields;
                }

                const query = `SELECT * FROM ${searchTable} WHERE ${searchFields.split(',').map(field => `${field.trim()} LIKE ?`).join(' OR ')} LIMIT 50`;
                const searchParams = searchFields.split(',').map(() => `%${q}%`);

                const [results] = await pool.query(query, searchParams);

                if (results.length > 0) {
                    searchResults.push({
                        table: searchTable,
                        results
                    });
                }
            } catch (tableError) {
                console.error(`${searchTable} arama hatası:`, tableError);
            }
        }

        res.json({
            success: true,
            data: {
                query: q,
                results: searchResults,
                totalResults: searchResults.reduce((sum, item) => sum + item.results.length, 0)
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Arama hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Arama sırasında hata oluştu'
        });
    }
});

// Ana sayfa
app.get('/', (req, res) => {
    res.json({
        success: true,
        message: 'Bormegdata API Sunucusu',
        version: '1.0.0',
        endpoints: {
            health: '/api/health',
            tables: '/api/tables',
            stats: '/api/stats',
            production: {
                silodata: '/api/production/silodata/:hatNumber',
                urunModelleri: '/api/production/urun-modelleri',
                keceStok: '/api/production/kece-stok'
            },
            hammadde: {
                anlik: '/api/hammadde/anlik',
                set: '/api/hammadde/set',
                stok: '/api/hammadde/stok'
            },
            barcode: {
                bilgileri: '/api/barcode/bilgileri',
                bilgileri2: '/api/barcode/bilgileri2'
            },
            general: {
                table: '/api/table/:tableName',
                search: '/api/search'
            }
        },
        timestamp: new Date().toISOString()
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint bulunamadı',
        path: req.originalUrl,
        timestamp: new Date().toISOString()
    });
});

// Error handler
app.use((error, req, res, next) => {
    console.error('Sunucu hatası:', error);
    res.status(500).json({
        success: false,
        error: 'Sunucu hatası oluştu',
        details: error.message,
        timestamp: new Date().toISOString()
    });
});

// Sunucuyu başlat
const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
    console.log('🚀 Bormegdata API Sunucusu başlatıldı!');
    console.log(`📡 Port: ${PORT}`);
    console.log(`🌐 URL: http://localhost:${PORT}`);
    console.log(`🔗 API Base: http://localhost:${PORT}/api`);
    console.log(`💚 Health Check: http://localhost:${PORT}/api/health`);
    console.log('⏰ Başlatma zamanı:', new Date().toISOString());
    console.log('='.repeat(50));
});

export default app; 