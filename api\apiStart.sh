#!/bin/bash

# Çalışan process'leri kontrol etmek için pgrep kullanılır.
if pgrep -f "/usr/bin/node /var/www/html/api/api-server.js" > /dev/null
then
  echo "api-server.js zaten çalışıyor."
else
  echo "api-server.js başlatılıyor..."
  # Arka planda çalıştırmak için & kullanılır. Çıktıyı /dev/null'a yönlendirerek konsolda görünmesini engelleriz.
  nohup /usr/bin/node /var/www/html/api/api-server.js > /dev/null 2>&1 &
  echo "api-server.js başlatıldı."
fi


exit 0
