
import { Routes, Route } from "react-router-dom";
import Login from "@/pages/Login";
import NotFound from "@/pages/NotFound";
import Index from "@/pages/Index";
import Musteriler from "@/pages/Musteriler";
import Bakim from "@/pages/Bakim";
import <PERSON>clar from "@/pages/Araclar";
import <PERSON>ki<PERSON><PERSON>onetimi from "@/pages/EkipYonetimi";
import Ayarlar from "@/pages/Ayarlar";
import Raporlar from "@/pages/Raporlar";
import Uretim from "@/pages/Uretim";
import Admin from "@/pages/Admin";
import Finans from "@/pages/Finans";
import Cari from "@/pages/finans/Cari";
import AlacakBorc from "@/pages/finans/AlacakBorc";
import Envanter from "@/pages/finans/Envanter";
import Nakit from "@/pages/finans/Nakit";
import FinansRaporlar from "@/pages/finans/FinansRaporlar";
import EDevlet from "@/pages/finans/EDevlet";
import BankaKasa from "@/pages/finans/BankaKasa";
import Faturalar from "@/pages/finans/Faturalar";
import Muhasebe from "@/pages/finans/Muhasebe";
import CekSenet from "@/pages/finans/CekSenet";
import Demirbas from "@/pages/finans/Demirbas";
import Stok from "@/pages/Stok";
import Hammadde from "@/pages/Hammadde";

import ProtectedRoute from "@/components/auth/ProtectedRoute";

export const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/login" element={<Login />} />

      {/* Main Routes */}
      <Route path="/" element={
        <ProtectedRoute>
          <Index />
        </ProtectedRoute>
      } />
      <Route path="/musteriler" element={
        <ProtectedRoute requiredModule="musteri">
          <Musteriler />
        </ProtectedRoute>
      } />
      <Route path="/bakim" element={
        <ProtectedRoute requiredModule="bakim">
          <Bakim />
        </ProtectedRoute>
      } />
      <Route path="/araclar" element={
        <ProtectedRoute requiredModule="araclar">
          <Araclar />
        </ProtectedRoute>
      } />
      <Route path="/ekip-yonetimi" element={
        <ProtectedRoute requiredModule="insan_kaynaklari">
          <EkipYonetimi />
        </ProtectedRoute>
      } />
      <Route path="/ayarlar" element={
        <ProtectedRoute>
          <Ayarlar />
        </ProtectedRoute>
      } />
      <Route path="/raporlar" element={
        <ProtectedRoute>
          <Raporlar />
        </ProtectedRoute>
      } />
      <Route path="/uretim" element={
        <ProtectedRoute requiredModule="uretim">
          <Uretim />
        </ProtectedRoute>
      } />
      <Route path="/admin" element={
        <ProtectedRoute requiredRole="admin">
          <Admin />
        </ProtectedRoute>
      } />

      {/* Finance Routes */}
      <Route path="/finans" element={
        <ProtectedRoute requiredModule="finans">
          <Finans />
        </ProtectedRoute>
      } />
      <Route path="/finans/cari" element={
        <ProtectedRoute requiredModule="finans">
          <Cari />
        </ProtectedRoute>
      } />
      <Route path="/finans/alacak-borc" element={
        <ProtectedRoute requiredModule="finans">
          <AlacakBorc />
        </ProtectedRoute>
      } />
      <Route path="/finans/envanter" element={
        <ProtectedRoute requiredModule="finans">
          <Envanter />
        </ProtectedRoute>
      } />
      <Route path="/finans/nakit" element={
        <ProtectedRoute requiredModule="finans">
          <Nakit />
        </ProtectedRoute>
      } />
      <Route path="/finans/raporlar" element={
        <ProtectedRoute requiredModule="finans">
          <FinansRaporlar />
        </ProtectedRoute>
      } />
      <Route path="/finans/e-devlet" element={
        <ProtectedRoute requiredModule="finans">
          <EDevlet />
        </ProtectedRoute>
      } />
      <Route path="/finans/banka-kasa" element={
        <ProtectedRoute requiredModule="finans">
          <BankaKasa />
        </ProtectedRoute>
      } />
      <Route path="/finans/faturalar" element={
        <ProtectedRoute requiredModule="finans">
          <Faturalar />
        </ProtectedRoute>
      } />
      <Route path="/finans/muhasebe" element={
        <ProtectedRoute requiredModule="finans">
          <Muhasebe />
        </ProtectedRoute>
      } />
      <Route path="/finans/cek-senet" element={
        <ProtectedRoute requiredModule="finans">
          <CekSenet />
        </ProtectedRoute>
      } />
      <Route path="/finans/demirbas" element={
        <ProtectedRoute requiredModule="finans">
          <Demirbas />
        </ProtectedRoute>
      } />
      <Route path="/finans/stok" element={
        <ProtectedRoute requiredModule="finans">
          <Stok />
        </ProtectedRoute>
      } />
      <Route path="/hammadde" element={
        <ProtectedRoute requiredModule="hammadde">
          <Hammadde />
        </ProtectedRoute>
      } />
      <Route path="/hammadde/veriler" element={
        <ProtectedRoute requiredModule="hammadde">
          <Hammadde />
        </ProtectedRoute>
      } />
      <Route path="/hammadde/stok-girisi" element={
        <ProtectedRoute requiredModule="hammadde">
          <Hammadde />
        </ProtectedRoute>
      } />


      {/* Optional Module Routes */}
      <Route path="/satis" element={
        <ProtectedRoute requiredModule="satis">
          <div>Satış Modülü</div>
        </ProtectedRoute>
      } />
      <Route path="/proje" element={
        <ProtectedRoute requiredModule="proje">
          <div>Proje Modülü</div>
        </ProtectedRoute>
      } />
      <Route path="/tedarik" element={
        <ProtectedRoute requiredModule="tedarik">
          <div>Tedarik Modülü</div>
        </ProtectedRoute>
      } />
      <Route path="/insaat" element={
        <ProtectedRoute requiredModule="insaat">
          <div>İnşaat Modülü</div>
        </ProtectedRoute>
      } />
      <Route path="/profesyonel" element={
        <ProtectedRoute requiredModule="profesyonel">
          <div>Profesyonel Modülü</div>
        </ProtectedRoute>
      } />
      <Route path="/servis" element={
        <ProtectedRoute requiredModule="servis">
          <div>Servis Modülü</div>
        </ProtectedRoute>
      } />
      <Route path="/saglik" element={
        <ProtectedRoute requiredModule="saglik">
          <div>Sağlık Modülü</div>
        </ProtectedRoute>
      } />
      <Route path="/konaklama" element={
        <ProtectedRoute requiredModule="konaklama">
          <div>Konaklama Modülü</div>
        </ProtectedRoute>
      } />

      {/* 404 Route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};
