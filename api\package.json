{"name": "bormegdata-api", "version": "1.1.0", "description": "Bormegdata API Server with Authentication", "main": "api-server.js", "type": "module", "scripts": {"start": "node api-server.js", "dev": "nodemon api-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["api", "express", "mysql", "authentication", "jwt", "bormegdata"], "author": "Bormeg Team", "license": "ISC", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "bcrypt": "^5.1.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}