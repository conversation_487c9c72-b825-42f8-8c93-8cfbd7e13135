import React from 'react';
import { format, addHours } from 'date-fns';
import { SiloData } from '@/models/SiloData';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";

interface UretimVerileriTablosuProps {
    veriler: SiloData[];
    getUrunAdi: (stokKodu: string) => string;
    getUrunCap: (stokKodu: string) => string;
    formatBoruAdi: (item: SiloData) => string;
    loading: boolean;
    showConversionColumns?: boolean;
}

const UretimVerileriTablosu: React.FC<UretimVerileriTablosuProps> = ({
    veriler,
    getUrunAdi,
    getUrunCap,
    formatBoruAdi,
    loading,
    showConversionColumns = false
}) => {
    // En son 200 veriyi göstermek için sınırlama
    const sonVeriler = veriler.slice(0, 200);

    if (loading) {
        return (
            <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    if (veriler.length === 0) {
        return (
            <div className="bg-white rounded-lg shadow-sm p-6 text-center border border-gray-100">
                <p className="text-gray-500">Gösterilecek veri bulunamadı.</p>
            </div>
        );
    }

    // Function to remove duplicate Ø symbols
    const fixBoruAdi = (boruAdi: string): string => {
        if (!boruAdi) return '';
        const duplicatePattern = /Ø\s*(\d+)\s*Ø\s*\1/g;
        return boruAdi.replace(duplicatePattern, 'Ø $1');
    };

    // Veritabanından gelen saati doğru gösterme fonksiyonu
    const formatSaat = (item: SiloData): string => {
        try {
            // Öncelikle veritabanından gelen doğrudan saat bilgisini kontrol et
            if (item.saat && typeof item.saat === 'string') {
                // String olarak gelen saati doğrudan döndür (artık saat eklemeye gerek yok)
                return item.saat;
            }

            // Tarih nesnesiyse
            let date: Date;
            if (item.tarih instanceof Date) {
                date = new Date(item.tarih);
            } else {
                // Tarih bir string veya başka bir değer ise
                const tarihStr = String(item.tarih || '');

                // ISO formatında T içeriyorsa (örn: "2025-05-09T23:45:00")
                if (tarihStr.includes('T')) {
                    const [datePart, timePart] = tarihStr.split('T');
                    return timePart.substring(0, 5); // HH:mm formatını döndür (saat eklemeye gerek yok)
                }

                // String formatını Date nesnesine çevir
                date = new Date(tarihStr);
                if (isNaN(date.getTime())) {
                    return '';
                }
            }

            // Date nesnesi varsa doğrudan göster (saat eklemeye gerek yok)
            return format(date, 'HH:mm');
        } catch (e) {
            console.error('Tarih biçimlendirme hatası:', e);
            return '';
        }
    };

    return (
        <div className="overflow-hidden rounded-lg shadow-sm border border-gray-200">
            <div className="bg-blue-50 p-2 border-b border-blue-100 flex items-center">
                <div className="w-1 h-4 bg-blue-600 rounded-full mr-2"></div>
                <span className="text-blue-600 font-medium text-sm">Veri Listesi: Son {sonVeriler.length} Kayıt</span>
            </div>
            <Table>
                <TableHeader className="bg-blue-600">
                    <TableRow>
                        <TableHead className="text-white font-medium text-xs py-2">KAYIT NO</TableHead>
                        <TableHead className="text-white font-medium text-xs py-2">TARİH, SAAT</TableHead>
                        <TableHead className="text-white font-medium text-xs py-2">BORU ADI</TableHead>
                        <TableHead className="text-white font-medium text-xs py-2">AĞIRLIK</TableHead>
                        <TableHead className="text-white font-medium text-xs py-2">ADET</TableHead>
                        {showConversionColumns && (
                            <>
                                <TableHead className="text-white font-medium text-xs py-2">DURUM</TableHead>
                                <TableHead className="text-white font-medium text-xs py-2">DÖNÜŞÜM TARİHİ</TableHead>
                                <TableHead className="text-white font-medium text-xs py-2">DÖNÜŞÜM ID</TableHead>
                                <TableHead className="text-white font-medium text-xs py-2">HAT NO</TableHead>
                            </>
                        )}
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {sonVeriler.map((item, index) => (
                        <TableRow key={index} className="hover:bg-gray-50">
                            <TableCell className="py-1.5 text-xs">{item.id}</TableCell>
                            <TableCell className="py-1.5">
                                <div className="flex items-center">
                                    <svg viewBox="0 0 24 24" className="w-2.5 h-2.5 text-gray-400 mr-1" stroke="currentColor" fill="none">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <div>
                                        <div className="text-xs font-medium">{formatSaat(item)}</div>
                                        <div className="text-gray-500 text-[10px]">{format(new Date(item.tarih), 'dd.MM.yyyy')}</div>
                                    </div>
                                </div>
                            </TableCell>
                            <TableCell className="py-1.5 text-xs">
                                {fixBoruAdi(formatBoruAdi(item))}
                            </TableCell>
                            <TableCell className="py-1.5 text-xs">
                                {typeof item.boruAg === 'number'
                                    ? item.boruAg.toFixed(1)
                                    : (typeof item.boruAg === 'string' && !isNaN(parseFloat(item.boruAg))
                                        ? parseFloat(item.boruAg).toFixed(1)
                                        : '-')}
                            </TableCell>
                            <TableCell className="py-1.5 text-xs">{sonVeriler.length - index}</TableCell>
                            {showConversionColumns && (
                                <>
                                    <TableCell className="py-1.5 text-xs">{item.durum || ''}</TableCell>
                                    <TableCell className="py-1.5 text-xs">{item.donusumTarihi ? format(addHours(item.donusumTarihi, 3), 'dd.MM.yyyy HH:mm') : ''}</TableCell>
                                    <TableCell className="py-1.5 text-xs">{item.donusumId ?? ''}</TableCell>
                                    <TableCell className="py-1.5 text-xs">{item.hat || ''}</TableCell>
                                </>
                            )}
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </div>
    );
};

export default UretimVerileriTablosu;
