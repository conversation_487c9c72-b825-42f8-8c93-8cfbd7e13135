import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { CalendarIcon, Save, X } from "lucide-react";
import { KeceStok, KECE_STOK_KODLARI, KECE_AGIRLIKLARI } from '@/models/KeceStok';
import { KeceStokService } from '@/services/keceStokService';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface KeceStokDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    keceStok?: KeceStok;
    onSuccess: () => void;
}

const KeceStokDialog: React.FC<KeceStokDialogProps> = ({
    open,
    onOpenChange,
    keceStok,
    onSuccess
}) => {
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState({
        tarih: new Date(),
        keceAg: '',
        stokKodu: '',
        yeniStokKodu: '',
        donusumTarihi: undefined as Date | undefined,
        durum: 'aktif',
        donusumId: '',
        barkodYazildi: false,
        aktarim: '',
        agirlikStabil: false
    });
    const { toast } = useToast();

    const isEdit = !!keceStok;

    // Form verilerini sıfırla
    const resetForm = () => {
        setFormData({
            tarih: new Date(),
            keceAg: '',
            stokKodu: '',
            yeniStokKodu: '',
            donusumTarihi: undefined,
            durum: 'aktif',
            donusumId: '',
            barkodYazildi: false,
            aktarim: '',
            agirlikStabil: false
        });
    };

    // Form gönderimi
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.keceAg) {
            toast({
                title: "Hata",
                description: "Lütfen keçe ağırlığını seçin.",
                variant: "destructive",
            });
            return;
        }

        try {
            setLoading(true);

            const keceStokData: any = {
                tarih: formData.tarih,
                keceAg: formData.keceAg,
                stokKodu: formData.stokKodu || null,
                yeniStokKodu: formData.yeniStokKodu || null,
                donusumTarihi: formData.donusumTarihi || null,
                durum: formData.durum,
                donusumId: formData.donusumId ? parseInt(formData.donusumId) : null,
                barkodYazildi: formData.barkodYazildi ? 1 : 0,
                aktarim: formData.aktarim || null,
                agirlikStabil: formData.agirlikStabil ? 1 : 0
            };

            if (isEdit && keceStok) {
                await KeceStokService.guncelleKeceStok(keceStok.id, keceStokData);
                toast({
                    title: "Başarılı",
                    description: "Keçe stok kaydı başarıyla güncellendi.",
                });
            } else {
                await KeceStokService.ekleKeceStok(keceStokData);
                toast({
                    title: "Başarılı",
                    description: "Keçe stok kaydı başarıyla eklendi.",
                });
            }

            onSuccess();
            onOpenChange(false);
            resetForm();
        } catch (error) {
            console.error('Keçe stok kaydedilirken hata:', error);
            toast({
                title: "Hata",
                description: "Keçe stok kaydı kaydedilirken bir hata oluştu.",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    };

    // Dialog kapandığında formu sıfırla
    const handleOpenChange = (open: boolean) => {
        if (!open) {
            resetForm();
        }
        onOpenChange(open);
    };

    // Düzenleme modunda form verilerini doldur
    useEffect(() => {
        if (keceStok && open) {
            setFormData({
                tarih: new Date(keceStok.tarih),
                keceAg: keceStok.keceAg || '',
                stokKodu: keceStok.stokKodu || '',
                yeniStokKodu: keceStok.yeniStokKodu || '',
                donusumTarihi: keceStok.donusumTarihi,
                durum: keceStok.durum,
                donusumId: keceStok.donusumId?.toString() || '',
                barkodYazildi: keceStok.barkodYazildi || false,
                aktarim: keceStok.aktarim || '',
                agirlikStabil: keceStok.agirlikStabil || false
            });
        }
    }, [keceStok, open]);

    return (
        <Dialog open={open} onOpenChange={handleOpenChange}>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle className="text-lg font-bold text-green-700">
                        {isEdit ? 'Keçe Stok Düzenle' : 'Yeni Keçe Stok Ekle'}
                    </DialogTitle>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-4">
                    {/* Tarih ve Keçe Ağırlığı */}
                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="tarih">Tarih *</Label>
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button
                                        variant="outline"
                                        className={cn(
                                            "justify-start text-left font-normal w-full",
                                            !formData.tarih && "text-muted-foreground"
                                        )}
                                    >
                                        <CalendarIcon className="mr-2 h-4 w-4" />
                                        {formData.tarih ? format(formData.tarih, "dd.MM.yyyy", { locale: tr }) : "Tarih seçin"}
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0">
                                    <Calendar
                                        mode="single"
                                        selected={formData.tarih}
                                        onSelect={(date) => date && setFormData(prev => ({ ...prev, tarih: date }))}
                                        initialFocus
                                    />
                                </PopoverContent>
                            </Popover>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="keceAg">Keçe Ağırlığı *</Label>
                            <Select
                                value={formData.keceAg}
                                onValueChange={(value) => setFormData(prev => ({ ...prev, keceAg: value }))}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Keçe ağırlığı seçin" />
                                </SelectTrigger>
                                <SelectContent>
                                    {KECE_AGIRLIKLARI.map(agirlik => (
                                        <SelectItem key={agirlik} value={agirlik}>
                                            {agirlik}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Stok Kodları */}
                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="stokKodu">Stok Kodu</Label>
                            <Select
                                value={formData.stokKodu || 'none'}
                                onValueChange={(value) => setFormData(prev => ({ ...prev, stokKodu: value === 'none' ? '' : value }))}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Stok kodu seçin" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="none">Seçiniz</SelectItem>
                                    {Object.entries(KECE_STOK_KODLARI).map(([kod, ad]) => (
                                        <SelectItem key={kod} value={kod}>
                                            {ad}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="yeniStokKodu">Yeni Stok Kodu</Label>
                            <Select
                                value={formData.yeniStokKodu || 'none'}
                                onValueChange={(value) => setFormData(prev => ({ ...prev, yeniStokKodu: value === 'none' ? '' : value }))}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Yeni stok kodu seçin" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="none">Seçiniz</SelectItem>
                                    {Object.entries(KECE_STOK_KODLARI).map(([kod, ad]) => (
                                        <SelectItem key={kod} value={kod}>
                                            {ad}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Dönüşüm Tarihi ve Durum */}
                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="donusumTarihi">Dönüşüm Tarihi</Label>
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button
                                        variant="outline"
                                        className={cn(
                                            "justify-start text-left font-normal w-full",
                                            !formData.donusumTarihi && "text-muted-foreground"
                                        )}
                                    >
                                        <CalendarIcon className="mr-2 h-4 w-4" />
                                        {formData.donusumTarihi ? format(formData.donusumTarihi, "dd.MM.yyyy", { locale: tr }) : "Dönüşüm tarihi seçin"}
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0">
                                    <Calendar
                                        mode="single"
                                        selected={formData.donusumTarihi}
                                        onSelect={(date) => setFormData(prev => ({ ...prev, donusumTarihi: date }))}
                                        initialFocus
                                    />
                                </PopoverContent>
                            </Popover>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="durum">Durum</Label>
                            <Select
                                value={formData.durum}
                                onValueChange={(value) => setFormData(prev => ({ ...prev, durum: value }))}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="aktif">Aktif</SelectItem>
                                    <SelectItem value="pasif">Pasif</SelectItem>
                                    <SelectItem value="tükenmiş">Tükenmiş</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Dönüşüm ID ve Aktarım */}
                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="donusumId">Dönüşüm ID</Label>
                            <Input
                                id="donusumId"
                                type="number"
                                value={formData.donusumId}
                                onChange={(e) => setFormData(prev => ({ ...prev, donusumId: e.target.value }))}
                                placeholder="Dönüşüm ID"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="aktarim">Aktarım</Label>
                            <Input
                                id="aktarim"
                                value={formData.aktarim}
                                onChange={(e) => setFormData(prev => ({ ...prev, aktarim: e.target.value }))}
                                placeholder="Aktarım bilgisi"
                            />
                        </div>
                    </div>

                    {/* Checkbox'lar */}
                    <div className="grid grid-cols-2 gap-4">
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="barkodYazildi"
                                checked={formData.barkodYazildi}
                                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, barkodYazildi: checked as boolean }))}
                            />
                            <Label htmlFor="barkodYazildi">Barkod Yazıldı</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="agirlikStabil"
                                checked={formData.agirlikStabil}
                                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, agirlikStabil: checked as boolean }))}
                            />
                            <Label htmlFor="agirlikStabil">Ağırlık Stabil</Label>
                        </div>
                    </div>

                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => handleOpenChange(false)}
                            disabled={loading}
                        >
                            <X className="h-4 w-4 mr-2" />
                            İptal
                        </Button>
                        <Button type="submit" disabled={loading}>
                            <Save className="h-4 w-4 mr-2" />
                            {loading ? 'Kaydediliyor...' : (isEdit ? 'Güncelle' : 'Kaydet')}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
};

export default KeceStokDialog; 