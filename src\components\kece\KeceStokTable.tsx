import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Filter, RefreshCw, Plus, Edit, Trash, Search, CheckCircle, XCircle } from "lucide-react";
import { KeceStok, KeceStokFiltre, KECE_STOK_KODLARI, KECE_AGIRLIKLARI } from '@/models/KeceStok';
import { KeceStokService } from '@/services/keceStokService';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import KeceStokDialog from './KeceStokDialog';

interface KeceStokTableProps {
    onRefresh?: () => void;
}

const KeceStokTable: React.FC<KeceStokTableProps> = ({ onRefresh }) => {
    const [keceStokListesi, setKeceStokListesi] = useState<KeceStok[]>([]);
    const [loading, setLoading] = useState(true);
    const [filtre, setFiltre] = useState<KeceStokFiltre>({});
    const [baslangicTarihi, setBaslangicTarihi] = useState<Date>();
    const [bitisTarihi, setBitisTarihi] = useState<Date>();
    const [searchTerm, setSearchTerm] = useState('');
    const [editingItem, setEditingItem] = useState<KeceStok | null>(null);
    const { toast } = useToast();

    // Keçe stok verilerini yükle
    const loadKeceStokData = async () => {
        try {
            setLoading(true);
            const filtreData: KeceStokFiltre = {
                ...filtre,
                baslangicTarihi: baslangicTarihi,
                bitisTarihi: bitisTarihi
            };

            const data = await KeceStokService.getKeceStokListesi(filtreData);
            setKeceStokListesi(data);
        } catch (error) {
            console.error('Keçe stok verileri yüklenirken hata:', error);
            toast({
                title: "Hata",
                description: "Keçe stok verileri yüklenirken bir hata oluştu.",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    };

    // Filtreleme fonksiyonu
    const filteredData = keceStokListesi.filter(item => {
        const searchMatch = searchTerm === '' ||
            (item.stokKodu && item.stokKodu.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (item.yeniStokKodu && item.yeniStokKodu.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (item.keceAg && item.keceAg.toLowerCase().includes(searchTerm.toLowerCase()));

        return searchMatch;
    });

    // Filtreleri temizle
    const clearFilters = () => {
        setFiltre({});
        setBaslangicTarihi(undefined);
        setBitisTarihi(undefined);
        setSearchTerm('');
    };

    // Keçe stok kaydını düzenle
    const handleEdit = (item: KeceStok) => {
        setEditingItem(item);
    };

    // Dialog başarılı işlem sonrası
    const handleDialogSuccess = () => {
        loadKeceStokData();
        onRefresh?.();
    };

    // Keçe stok kaydını sil
    const handleDelete = async (id: number) => {
        if (!confirm('Bu kaydı silmek istediğinizden emin misiniz?')) {
            return;
        }

        try {
            await KeceStokService.silKeceStok(id);
            toast({
                title: "Başarılı",
                description: "Keçe stok kaydı başarıyla silindi.",
            });
            loadKeceStokData();
            onRefresh?.();
        } catch (error) {
            console.error('Keçe stok silinirken hata:', error);
            toast({
                title: "Hata",
                description: "Keçe stok kaydı silinirken bir hata oluştu.",
                variant: "destructive",
            });
        }
    };

    // Durum badge rengi
    const getStatusBadgeVariant = (durum: string) => {
        switch (durum.toLowerCase()) {
            case 'aktif':
                return 'default';
            case 'pasif':
                return 'secondary';
            case 'tükenmiş':
                return 'destructive';
            default:
                return 'outline';
        }
    };

    useEffect(() => {
        loadKeceStokData();
    }, [filtre, baslangicTarihi, bitisTarihi]);

    return (
        <div className="space-y-6">
            {/* Filtreler */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Arama */}
                <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="Ara..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>

                {/* Keçe Ağırlığı Filtresi */}
                <Select
                    value={filtre.keceAg || 'all'}
                    onValueChange={(value) => setFiltre(prev => ({ ...prev, keceAg: value === 'all' ? undefined : value }))}
                >
                    <SelectTrigger>
                        <SelectValue placeholder="Keçe Ağırlığı Seçin" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">Tümü</SelectItem>
                        {KECE_AGIRLIKLARI.map(agirlik => (
                            <SelectItem key={agirlik} value={agirlik}>
                                {agirlik}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>

                {/* Stok Kodu Filtresi */}
                <Select
                    value={filtre.stokKodu || 'all'}
                    onValueChange={(value) => setFiltre(prev => ({ ...prev, stokKodu: value === 'all' ? undefined : value }))}
                >
                    <SelectTrigger>
                        <SelectValue placeholder="Stok Kodu Seçin" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">Tümü</SelectItem>
                        {Object.entries(KECE_STOK_KODLARI).map(([kod, ad]) => (
                            <SelectItem key={kod} value={kod}>
                                {ad}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>

                {/* Başlangıç Tarihi */}
                <Popover>
                    <PopoverTrigger asChild>
                        <Button
                            variant="outline"
                            className={cn(
                                "justify-start text-left font-normal",
                                !baslangicTarihi && "text-muted-foreground"
                            )}
                        >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {baslangicTarihi ? format(baslangicTarihi, "dd.MM.yyyy", { locale: tr }) : "Başlangıç Tarihi"}
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                        <Calendar
                            mode="single"
                            selected={baslangicTarihi}
                            onSelect={setBaslangicTarihi}
                            initialFocus
                        />
                    </PopoverContent>
                </Popover>
            </div>

            {/* Tablo */}
            <div className="rounded-md border bg-white">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-16">ID</TableHead>
                            <TableHead className="w-24">Tarih</TableHead>
                            <TableHead>Keçe Ağırlığı</TableHead>
                            <TableHead>Stok Kodu</TableHead>
                            <TableHead>Yeni Stok Kodu</TableHead>
                            <TableHead className="w-24">Durum</TableHead>
                            <TableHead className="w-20">Barkod</TableHead>
                            <TableHead className="w-20">Ağırlık</TableHead>
                            <TableHead className="w-32">İşlemler</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {loading ? (
                            <TableRow>
                                <TableCell colSpan={9} className="text-center py-8">
                                    <div className="flex items-center justify-center space-x-2">
                                        <RefreshCw className="h-4 w-4 animate-spin" />
                                        <span>Yükleniyor...</span>
                                    </div>
                                </TableCell>
                            </TableRow>
                        ) : filteredData.length === 0 ? (
                            <TableRow>
                                <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                                    Veri bulunamadı
                                </TableCell>
                            </TableRow>
                        ) : (
                            filteredData.map((item) => (
                                <TableRow key={item.id}>
                                    <TableCell className="font-medium">{item.id}</TableCell>
                                    <TableCell>
                                        {format(new Date(item.tarih), 'dd.MM.yyyy', { locale: tr })}
                                    </TableCell>
                                    <TableCell>{item.keceAg || '-'}</TableCell>
                                    <TableCell className="font-mono text-sm">{item.stokKodu || '-'}</TableCell>
                                    <TableCell className="font-mono text-sm">{item.yeniStokKodu || '-'}</TableCell>
                                    <TableCell>
                                        <Badge variant={getStatusBadgeVariant(item.durum)}>
                                            {item.durum}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>
                                        {item.barkodYazildi ? (
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                        ) : (
                                            <XCircle className="h-4 w-4 text-red-600" />
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        {item.agirlikStabil ? (
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                        ) : (
                                            <XCircle className="h-4 w-4 text-red-600" />
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex space-x-1">
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => handleEdit(item)}
                                            >
                                                <Edit className="h-3 w-3" />
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="destructive"
                                                onClick={() => handleDelete(item.id)}
                                            >
                                                <Trash className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))
                        )}
                    </TableBody>
                </Table>
            </div>

            {/* Özet Bilgiler */}
            {!loading && filteredData.length > 0 && (
                <div className="p-4 bg-muted rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span className="font-medium">Toplam Kayıt:</span> {filteredData.length}
                        </div>
                        <div>
                            <span className="font-medium">Aktif Kayıt:</span> {filteredData.filter(item => item.durum === 'aktif').length}
                        </div>
                        <div>
                            <span className="font-medium">Son Güncelleme:</span> {format(new Date(), 'dd.MM.yyyy HH:mm', { locale: tr })}
                        </div>
                    </div>
                </div>
            )}

            {/* Keçe Stok Dialog */}
            <KeceStokDialog
                open={!!editingItem}
                onOpenChange={(open) => !open && setEditingItem(null)}
                keceStok={editingItem || undefined}
                onSuccess={handleDialogSuccess}
            />
        </div>
    );
};

export default KeceStokTable; 