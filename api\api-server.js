import express from 'express';
import mysql from 'mysql2/promise';
import cors from 'cors';

const app = express();

// CORS ayarları
app.use(cors({
    origin: ['http://bormeg.fun', 'http://localhost:3000', 'http://localhost:3001'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true
}));

// JSON body parser
app.use(express.json());

// Veritabanı bağlantı havuzu
const pool = mysql.createPool({
    host: '*************',
    user: 'mehmet',
    password: 'Mb_07112024',
    database: 'borudata',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    timezone: '+03:00'
});

// Utility fonksiyonlar
const buildDateFilter = (startDate, endDate, dateField = 'tarih') => {
    if (startDate && endDate) {
        return `AND DATE(${dateField}) >= ? AND DATE(${dateField}) <= ?`;
    } else if (startDate) {
        return `AND DATE(${dateField}) >= ?`;
    } else if (endDate) {
        return `AND DATE(${dateField}) <= ?`;
    }
    return '';
};

const buildPagination = (page = 1, limit = 50) => {
    const offset = (page - 1) * limit;
    return { limit: parseInt(limit), offset: parseInt(offset) };
};

// Ana sayfa
app.get('/', (req, res) => {
    res.json({
        success: true,
        message: 'Bormegdata API Sunucusu - Yeniden Düzenlenmiş',
        version: '2.0.0',
        baseUrl: 'http://bormeg.fun:3002/api',
        endpoints: [
            '/api/boru-stok - Boru Stok Verileri (silodata_genel)',
            '/api/hammadde-stok - Ham Madde Stok Verileri',
            '/api/hammadde-anlik - Ham Madde Anlık Vereler',
            '/api/hammadde-set - Ham Madde Set Verileri',
            '/api/kece-stok - Keçe Stok Verileri',
            '/api/urunler - Ürün Modelleri'
        ],
        timestamp: new Date().toISOString()
    });
});

// Health Check
app.get('/api/health', async (req, res) => {
    try {
        const connection = await pool.getConnection();
        connection.release();

        res.json({
            success: true,
            status: 'healthy',
            database: 'borudata',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Veritabanı bağlantısı başarısız'
        });
    }
});

// ========================================
// ANA API ENDPOINTS
// ========================================

// 1. BORU STOK VERİLERİ (silodata_genel)
app.get('/api/boru-stok', async (req, res) => {
    try {
        const {
            page = 1,
            limit = 15000,
            startDate,
            endDate,
            stokKodu,
            orderBy = 'tarih',
            order = 'DESC'
        } = req.query;

        const { limit: limitNum, offset } = buildPagination(page, limit);

        let query = 'SELECT * FROM silodata_genel WHERE 1=1';
        const params = [];

        // Tarih filtresi
        if (startDate || endDate) {
            query += ` ${buildDateFilter(startDate, endDate, 'tarih')}`;
            if (startDate) params.push(startDate);
            if (endDate) params.push(endDate);
        }

        // Stok kodu filtresi
        if (stokKodu) {
            query += ' AND stok_kodu = ?';
            params.push(stokKodu);
        }

        // Sıralama ve sayfalama
        query += ` ORDER BY ${orderBy} ${order} LIMIT ? OFFSET ?`;
        params.push(limitNum, offset);

        const [results] = await pool.query(query, params);

        // Toplam kayıt sayısı
        let countQuery = 'SELECT COUNT(*) as total FROM silodata_genel WHERE 1=1';
        const countParams = [];

        if (startDate || endDate) {
            countQuery += ` ${buildDateFilter(startDate, endDate, 'tarih')}`;
            if (startDate) countParams.push(startDate);
            if (endDate) countParams.push(endDate);
        }

        if (stokKodu) {
            countQuery += ' AND stok_kodu = ?';
            countParams.push(stokKodu);
        }

        const [countResult] = await pool.query(countQuery, countParams);
        const total = countResult[0].total;

        res.json({
            success: true,
            data: results,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                totalPages: Math.ceil(total / parseInt(limit))
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Boru stok hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Boru stok verileri alınırken hata oluştu'
        });
    }
});

// 2. HAM MADDE STOK VERİLERİ
app.get('/api/hammadde-stok', async (req, res) => {
    try {
        const {
            page = 1,
            limit = 50,
            startDate,
            endDate,
            stokKodu,
            orderBy = 'ID',
            order = 'DESC'
        } = req.query;

        const { limit: limitNum, offset } = buildPagination(page, limit);

        let query = 'SELECT * FROM HammaddeStok WHERE 1=1';
        const params = [];

        // Tarih filtresi (eğer tarih alanı varsa)
        if (startDate || endDate) {
            query += ` ${buildDateFilter(startDate, endDate, 'Tarih')}`;
            if (startDate) params.push(startDate);
            if (endDate) params.push(endDate);
        }

        // Stok kodu filtresi
        if (stokKodu) {
            query += ' AND StokKodu = ?';
            params.push(stokKodu);
        }

        // Sıralama ve sayfalama
        query += ` ORDER BY ${orderBy} ${order} LIMIT ? OFFSET ?`;
        params.push(limitNum, offset);

        const [results] = await pool.query(query, params);

        // Toplam sayı
        let countQuery = 'SELECT COUNT(*) as total FROM HammaddeStok WHERE 1=1';
        const countParams = [];

        if (startDate || endDate) {
            countQuery += ` ${buildDateFilter(startDate, endDate, 'Tarih')}`;
            if (startDate) countParams.push(startDate);
            if (endDate) countParams.push(endDate);
        }

        if (stokKodu) {
            countQuery += ' AND StokKodu = ?';
            countParams.push(stokKodu);
        }

        const [countResult] = await pool.query(countQuery, countParams);
        const total = countResult[0].total;

        res.json({
            success: true,
            data: results,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                totalPages: Math.ceil(total / parseInt(limit))
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Ham madde stok hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Ham madde stok verileri alınırken hata oluştu'
        });
    }
});

// 3. HAM MADDE ANLIK VERİLER
app.get('/api/hammadde-anlik', async (req, res) => {
    try {
        const {
            page = 1,
            limit = 50,
            startDate,
            endDate,
            stokKodu,
            dozajNo,
            orderBy = 'ID',
            order = 'DESC'
        } = req.query;

        const { limit: limitNum, offset } = buildPagination(page, limit);

        let query = 'SELECT * FROM HammaddeAnlik WHERE 1=1';
        const params = [];

        // Tarih filtresi
        if (startDate || endDate) {
            query += ` ${buildDateFilter(startDate, endDate, 'Tarih')}`;
            if (startDate) params.push(startDate);
            if (endDate) params.push(endDate);
        }

        // Stok kodu filtresi
        if (stokKodu) {
            query += ' AND StokKodu = ?';
            params.push(stokKodu);
        }

        // Dozaj no filtresi
        if (dozajNo) {
            query += ' AND DozajNo = ?';
            params.push(dozajNo);
        }

        // Sıralama ve sayfalama
        query += ` ORDER BY ${orderBy} ${order} LIMIT ? OFFSET ?`;
        params.push(limitNum, offset);

        const [results] = await pool.query(query, params);

        // Toplam sayı
        let countQuery = 'SELECT COUNT(*) as total FROM HammaddeAnlik WHERE 1=1';
        const countParams = [];

        if (startDate || endDate) {
            countQuery += ` ${buildDateFilter(startDate, endDate, 'Tarih')}`;
            if (startDate) countParams.push(startDate);
            if (endDate) countParams.push(endDate);
        }

        if (stokKodu) {
            countQuery += ' AND StokKodu = ?';
            countParams.push(stokKodu);
        }

        if (dozajNo) {
            countQuery += ' AND DozajNo = ?';
            countParams.push(dozajNo);
        }

        const [countResult] = await pool.query(countQuery, countParams);
        const total = countResult[0].total;

        res.json({
            success: true,
            data: results,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                totalPages: Math.ceil(total / parseInt(limit))
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Ham madde anlık hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Ham madde anlık verileri alınırken hata oluştu'
        });
    }
});

// 4. HAM MADDE SET VERİLERİ
app.get('/api/hammadde-set', async (req, res) => {
    try {
        const {
            page = 1,
            limit = 50,
            startDate,
            endDate,
            stokKodu,
            orderBy = 'ID',
            order = 'DESC'
        } = req.query;

        const { limit: limitNum, offset } = buildPagination(page, limit);

        let query = 'SELECT * FROM HammaddeSet WHERE 1=1';
        const params = [];

        // Tarih filtresi
        if (startDate || endDate) {
            query += ` ${buildDateFilter(startDate, endDate, 'Tarih')}`;
            if (startDate) params.push(startDate);
            if (endDate) params.push(endDate);
        }

        // Stok kodu filtresi
        if (stokKodu) {
            query += ' AND StokKodu = ?';
            params.push(stokKodu);
        }

        // Sıralama ve sayfalama
        query += ` ORDER BY ${orderBy} ${order} LIMIT ? OFFSET ?`;
        params.push(limitNum, offset);

        const [results] = await pool.query(query, params);

        // Toplam sayı
        let countQuery = 'SELECT COUNT(*) as total FROM HammaddeSet WHERE 1=1';
        const countParams = [];

        if (startDate || endDate) {
            countQuery += ` ${buildDateFilter(startDate, endDate, 'Tarih')}`;
            if (startDate) countParams.push(startDate);
            if (endDate) countParams.push(endDate);
        }

        if (stokKodu) {
            countQuery += ' AND StokKodu = ?';
            countParams.push(stokKodu);
        }

        const [countResult] = await pool.query(countQuery, countParams);
        const total = countResult[0].total;

        res.json({
            success: true,
            data: results,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                totalPages: Math.ceil(total / parseInt(limit))
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Ham madde set hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Ham madde set verileri alınırken hata oluştu'
        });
    }
});

// 5. KEÇE STOK VERİLERİ
app.get('/api/kece-stok', async (req, res) => {
    try {
        const {
            page = 1,
            limit = 50,
            startDate,
            endDate,
            stokKodu,
            durum,
            orderBy = 'ID',
            order = 'DESC'
        } = req.query;

        const { limit: limitNum, offset } = buildPagination(page, limit);

        let query = 'SELECT * FROM keceStok WHERE 1=1';
        const params = [];

        // Tarih filtresi
        if (startDate || endDate) {
            query += ` ${buildDateFilter(startDate, endDate, 'tarih')}`;
            if (startDate) params.push(startDate);
            if (endDate) params.push(endDate);
        }

        // Stok kodu filtresi
        if (stokKodu) {
            query += ' AND stok_kodu = ?';
            params.push(stokKodu);
        }

        // Durum filtresi
        if (durum) {
            query += ' AND durum = ?';
            params.push(durum);
        }

        // Sıralama ve sayfalama
        query += ` ORDER BY ${orderBy} ${order} LIMIT ? OFFSET ?`;
        params.push(limitNum, offset);

        const [results] = await pool.query(query, params);

        // Toplam sayı
        let countQuery = 'SELECT COUNT(*) as total FROM keceStok WHERE 1=1';
        const countParams = [];

        if (startDate || endDate) {
            countQuery += ` ${buildDateFilter(startDate, endDate, 'tarih')}`;
            if (startDate) countParams.push(startDate);
            if (endDate) countParams.push(endDate);
        }

        if (stokKodu) {
            countQuery += ' AND stok_kodu = ?';
            countParams.push(stokKodu);
        }

        if (durum) {
            countQuery += ' AND durum = ?';
            countParams.push(durum);
        }

        const [countResult] = await pool.query(countQuery, countParams);
        const total = countResult[0].total;

        res.json({
            success: true,
            data: results,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                totalPages: Math.ceil(total / parseInt(limit))
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Keçe stok hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Keçe stok verileri alınırken hata oluştu'
        });
    }
});

// 6. ÜRÜN MODELLERİ
app.get('/api/urunler', async (req, res) => {
    try {
        const {
            page = 1,
            limit = 50,
            stokKodu,
            aktif,
            search,
            orderBy = 'stok_adi',
            order = 'ASC'
        } = req.query;

        const { limit: limitNum, offset } = buildPagination(page, limit);

        let query = 'SELECT * FROM urun_modelleri WHERE 1=1';
        const params = [];

        // Stok kodu filtresi
        if (stokKodu) {
            query += ' AND stok_kodu = ?';
            params.push(stokKodu);
        }

        // Aktif filtresi
        if (aktif !== undefined) {
            query += ' AND aktif = ?';
            params.push(aktif === 'true' ? 1 : 0);
        }

        // Arama filtresi
        if (search) {
            query += ' AND (stok_adi LIKE ? OR stok_kodu LIKE ?)';
            params.push(`%${search}%`, `%${search}%`);
        }

        // Sıralama ve sayfalama
        query += ` ORDER BY ${orderBy} ${order} LIMIT ? OFFSET ?`;
        params.push(limitNum, offset);

        const [results] = await pool.query(query, params);

        // Toplam sayı
        let countQuery = 'SELECT COUNT(*) as total FROM urun_modelleri WHERE 1=1';
        const countParams = [];

        if (stokKodu) {
            countQuery += ' AND stok_kodu = ?';
            countParams.push(stokKodu);
        }

        if (aktif !== undefined) {
            countQuery += ' AND aktif = ?';
            countParams.push(aktif === 'true' ? 1 : 0);
        }

        if (search) {
            countQuery += ' AND (stok_adi LIKE ? OR stok_kodu LIKE ?)';
            countParams.push(`%${search}%`, `%${search}%`);
        }

        const [countResult] = await pool.query(countQuery, countParams);
        const total = countResult[0].total;

        res.json({
            success: true,
            data: results,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                totalPages: Math.ceil(total / parseInt(limit))
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Ürün modelleri hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Ürün modelleri alınırken hata oluştu'
        });
    }
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint bulunamadı',
        path: req.originalUrl,
        timestamp: new Date().toISOString()
    });
});

// Error handler
app.use((error, req, res, next) => {
    console.error('Sunucu hatası:', error);
    res.status(500).json({
        success: false,
        error: 'Sunucu hatası oluştu',
        details: error.message,
        timestamp: new Date().toISOString()
    });
});

// Sunucuyu başlat
const PORT = process.env.PORT || 3002;

app.listen(PORT, '0.0.0.0', () => {
    console.log('🚀 Bormegdata API Sunucusu (Yeniden Düzenlenmiş) başlatıldı!');
    console.log(`📡 Port: ${PORT}`);
    console.log(`🌐 URL: http://bormeg.fun:${PORT}`);
    console.log(`🔗 API Base: http://bormeg.fun:${PORT}/api`);
    console.log(`💚 Health Check: http://bormeg.fun:${PORT}/api/health`);
    console.log('📋 Endpoint\'ler:');
    console.log('   • /api/boru-stok - Boru Stok Verileri');
    console.log('   • /api/hammadde-stok - Ham Madde Stok');
    console.log('   • /api/hammadde-anlik - Ham Madde Anlık');
    console.log('   • /api/hammadde-set - Ham Madde Set');
    console.log('   • /api/kece-stok - Keçe Stok');
    console.log('   • /api/urunler - Ürün Modelleri');
    console.log('⏰ Başlatma zamanı:', new Date().toISOString());
    console.log('='.repeat(70));
});

export default app;