import React, { useState } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Pencil, Trash, Save, X } from 'lucide-react';
import { SiloData } from '@/models/SiloData';
import { UrunModel } from '@/models/UrunModel';

// Hat 5 için özel ürün listesi - Sadece KEÇE ürünleri
const Hat5UrunData = {
  boruTipleri: ["Ø 80", "Ø 100", "Ø 125", "Ø 160", "Ø 200"],
  altUrunler: {
    "Ø 80": [
      { ad: "Ø 80 KEÇE", stokKodu: "KC-STK080" }
    ],
    "Ø 100": [
      { ad: "Ø 100 KEÇE", stokKodu: "KC-STK100" }
    ],
    "Ø 125": [
      { ad: "Ø 125 KEÇE", stokKodu: "KC-STK125" }
    ],
    "Ø 160": [
      { ad: "Ø 160 KEÇE", stokKodu: "KC-STK160" }
    ],
    "Ø 200": [
      { ad: "Ø 200 KEÇE", stokKodu: "KC-STK200" }
    ]
  }
};

interface SiloDataTable5Props {
  siloDataList: SiloData[];
  urunAdlari: Record<string, string>;
  urunler: UrunModel[];
  handleUpdateSiloData: (id: number, stokKodu: string) => Promise<void>;
  handleDeleteSiloData: (id: number) => Promise<void>;
}

const SiloDataTable5: React.FC<SiloDataTable5Props> = ({
  siloDataList,
  urunAdlari,
  urunler,
  handleUpdateSiloData,
  handleDeleteSiloData
}) => {
  const [editingItem, setEditingItem] = useState<number | null>(null);
  const [editBoruTipi, setEditBoruTipi] = useState<string>("");
  const [editAltUrun, setEditAltUrun] = useState<string>("");

  // Düzenleme modunda seçilen boru tipine göre alt ürünleri getir
  const getEditAltUrunler = () => {
    if (!editBoruTipi || !Hat5UrunData.altUrunler[editBoruTipi]) {
      return [];
    }
    return Hat5UrunData.altUrunler[editBoruTipi];
  };

  const handleEditItem = (item: SiloData) => {
    setEditingItem(item.id);

    // Stok kodundan boru tipini ve alt ürünü bul
    for (const boruTipi of Hat5UrunData.boruTipleri) {
      const altUrunler = Hat5UrunData.altUrunler[boruTipi];
      const altUrun = altUrunler.find(urun => urun.stokKodu === item.stokKodu);

      if (altUrun) {
        setEditBoruTipi(boruTipi);
        setEditAltUrun(item.stokKodu);
        break;
      }
    }
  };

  const handleSaveEdit = async (itemId: number) => {
    if (!editAltUrun) return;

    try {
      await handleUpdateSiloData(itemId, editAltUrun);
      setEditingItem(null);
      setEditBoruTipi("");
      setEditAltUrun("");
    } catch (error) {
      console.error("Güncelleme hatası:", error);
    }
  };

  const handleCancelEdit = () => {
    setEditingItem(null);
    setEditBoruTipi("");
    setEditAltUrun("");
  };

  const handleEditBoruTipiChange = (value: string) => {
    setEditBoruTipi(value);
    setEditAltUrun("");
  };

  const formatSaat = (item: SiloData): string => {
    try {
      if (item.saat && typeof item.saat === 'string') {
        return item.saat;
      }

      let date: Date;
      if (item.tarih instanceof Date) {
        date = new Date(item.tarih);
      } else {
        const tarihStr = String(item.tarih || '');
        date = new Date(tarihStr);
        if (isNaN(date.getTime())) {
          return '';
        }
      }

      return format(new Date(date), 'HH:mm');
    } catch (e) {
      console.error('Tarih biçimlendirme hatası:', e);
      return '';
    }
  };

  const filteredData = siloDataList.slice(0, 200);

  return (
    <Card className="mt-6 w-full">
      <CardHeader>
        <CardTitle className="text-lg font-bold text-green-700 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          Keçe - Veri Düzenleme (KEÇE Ürünleri)
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-16">ID</TableHead>
                <TableHead className="w-24">Tarih</TableHead>
                <TableHead className="w-20">Saat</TableHead>
                <TableHead>Boru Adı</TableHead>
                <TableHead className="w-24">Boru Ağırlığı</TableHead>
                <TableHead className="w-32">Stok Kodu</TableHead>
                <TableHead className="w-32">İşlemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.id}</TableCell>
                  <TableCell>
                    {format(new Date(item.tarih), 'dd.MM.yyyy', { locale: tr })}
                  </TableCell>
                  <TableCell>{formatSaat(item)}</TableCell>
                  <TableCell>{item.boruAd || urunAdlari[item.stokKodu] || 'Bilinmeyen'}</TableCell>
                  <TableCell>{item.boruAg ? `${item.boruAg} kg` : '-'}</TableCell>
                  <TableCell>
                    {editingItem === item.id ? (
                      <div className="space-y-2">
                        <Select value={editBoruTipi} onValueChange={handleEditBoruTipiChange}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Boru tipi seçin" />
                          </SelectTrigger>
                          <SelectContent>
                            {Hat5UrunData.boruTipleri.map(tip => (
                              <SelectItem key={tip} value={tip}>
                                {tip}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Select
                          value={editAltUrun}
                          onValueChange={setEditAltUrun}
                          disabled={!editBoruTipi}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Alt ürün seçin" />
                          </SelectTrigger>
                          <SelectContent>
                            {getEditAltUrunler().map(urun => (
                              <SelectItem key={urun.stokKodu} value={urun.stokKodu}>
                                {urun.ad}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    ) : (
                      <span className="text-sm font-mono">{item.stokKodu}</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {editingItem === item.id ? (
                      <div className="flex space-x-1">
                        <Button
                          size="sm"
                          onClick={() => handleSaveEdit(item.id)}
                          disabled={!editAltUrun}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Save className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleCancelEdit}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ) : (
                      <div className="flex space-x-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditItem(item)}
                        >
                          <Pencil className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDeleteSiloData(item.id)}
                        >
                          <Trash className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default SiloDataTable5;
