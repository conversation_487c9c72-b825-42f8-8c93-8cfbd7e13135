# BORMEGDATA API KULLANIM KILAVUZU

**Base URL:** `http://bormeg.fun:3002`  


## 🏭 ÜRETİM ENDPOINTS

### Ürün Modelleri

#### Tüm Ürün Modelleri
```http
GET http://bormeg.fun:3002/api/production/urun-modelleri
```

#### Aktif <PERSON>
```http
GET http://bormeg.fun:3002/api/production/urun-modelleri?aktif=true
```

#### Stok Kodu ile Filtreleme
```http
GET http://bormeg.fun:3002/api/production/urun-modelleri?stokKodu=BRU001
```

#### Arama
```http
GET http://bormeg.fun:3002/api/production/urun-modelleri?search=boru
```

#### Kombinasyon Filtresi
```http
GET http://bormeg.fun:3002/api/production/urun-modelleri?aktif=true&search=PE&stokKodu=BRU001
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "stok_kodu": "BRU001",
      "stok_adi": "PE100 Boru 50mm",
      "hedef_agirlik": 2.5,
      "uzunluk": 100,
      "aktif": 1
    }
  ],
  "count": 1,
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

### Silodata (Üretim Hatları)

#### Hat 1 Verileri
```http
GET http://bormeg.fun:3002/api/production/silodata/1
```

#### Hat 2 Verileri (Tarih Filtreli)
```http
GET http://bormeg.fun:3002/api/production/silodata/2?startDate=2024-01-01&endDate=2024-01-31
```

#### Hat 3 Verileri (Stok Kodu + Sayfalama)
```http
GET http://bormeg.fun:3002/api/production/silodata/3?stokKodu=BRU001&page=1&limit=50
```

#### Hat 4 Verileri (Durum Filtreli)
```http
GET http://bormeg.fun:3002/api/production/silodata/4?durum=tamamlandi
```

#### Hat 5 Verileri (Sıralama)
```http
GET http://bormeg.fun:3002/api/production/silodata/5?orderBy=tarih&order=ASC
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 12345,
      "stok_kodu": "BRU001",
      "tarih": "2024-01-27T08:00:00.000Z",
      "agirlik": 2.45,
      "durum": "tamamlandi",
      "stok_adi": "PE100 Boru 50mm",
      "hedef_agirlik": 2.5,
      "uzunluk": 100
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 1250,
    "totalPages": 25
  },
  "filters": {
    "hatNumber": "1"
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

### Keçe Stok Yönetimi

#### Tüm Keçe Stok Verileri
```http
GET http://bormeg.fun:3002/api/production/kece-stok
```

#### Tarih Filtreli Keçe Stok
```http
GET http://bormeg.fun:3002/api/production/kece-stok?startDate=2024-01-01&endDate=2024-01-31
```

#### Stok Kodu + Durum Filtreli
```http
GET http://bormeg.fun:3002/api/production/kece-stok?stokKodu=KEC001&durum=aktif
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "ID": 1,
      "stok_kodu": "KEC001",
      "tarih": "2024-01-27T08:00:00.000Z",
      "miktar": 100,
      "durum": "aktif"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 345,
    "totalPages": 7
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

---

## 🧪 HAM MADDE ENDPOINTS

### Ham Madde Anlık Veriler

#### Tüm Anlık Veriler
```http
GET http://bormeg.fun:3002/api/hammadde/anlik
```

#### Tarih Filtreli
```http
GET http://bormeg.fun:3002/api/hammadde/anlik?startDate=2024-01-01&endDate=2024-01-31
```

#### Dozaj Numarası ile Filtreleme
```http
GET http://bormeg.fun:3002/api/hammadde/anlik?dozajNo=1
```

#### Tarih + Dozaj Kombinasyonu
```http
GET http://bormeg.fun:3002/api/hammadde/anlik?startDate=2024-01-15&dozajNo=2
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "ID": 1,
      "Tarih": "2024-01-27T08:00:00.000Z",
      "DozajNo": 1,
      "Miktar": 150.5,
      "Birim": "kg"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 850,
    "totalPages": 17
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

### Ham Madde Set Verileri
```http
GET http://bormeg.fun:3002/api/hammadde/set
```

#### Sayfalama ile
```http
GET http://bormeg.fun:3002/api/hammadde/set?page=2&limit=25
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "ID": 1,
      "SetAdi": "Karışım A",
      "Tarih": "2024-01-27T08:00:00.000Z",
      "ToplamMiktar": 500
    }
  ],
  "pagination": {
    "page": 2,
    "limit": 25,
    "total": 120,
    "totalPages": 5
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

### Ham Madde Stok Verileri
```http
GET http://bormeg.fun:3002/api/hammadde/stok
```

#### Sayfalama ile
```http
GET http://bormeg.fun:3002/api/hammadde/stok?page=1&limit=100
```

**Örnek Response:**
```json
{
  "success": true,
  "data": [
    {
      "ID": 1,
      "MalzemeAdi": "PE Granül",
      "StokMiktari": 1250.75,
      "Birim": "kg",
      "SonGuncellemeTarihi": "2024-01-27T08:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 100,
    "total": 65,
    "totalPages": 1
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

## 🔍 GENEL ENDPOINTS

### Tablo Verisi (Genel)

#### Silodata Tablosu
```http
GET http://bormeg.fun:3002/api/table/silodata?limit=100&offset=0
```

#### Ürün Modelleri Tablosu (Sıralama ile)
```http
GET http://bormeg.fun:3002/api/table/urun_modelleri?orderBy=stok_adi&order=ASC
```

**Örnek Response:**
```json
{
  "success": true,
  "data": {
    "table": "urun_modelleri",
    "results": [
      {
        "id": 1,
        "stok_kodu": "BRU001",
        "stok_adi": "PE100 Boru 50mm",
        "aktif": 1
      }
    ],
    "count": 1
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

**Desteklenen Tablolar:**
- `silodata`, `silodata2`, `silodata3`, `silodata4`, `silodata5`
- `urun_modelleri`
- `keceStok`
- `HammaddeAnlik`, `HammaddeSet`, `HammaddeStok`
- `barkod_bilgileri`, `barkod_bilgileri2`, `barkod_bilgileri3`
- `gunluk_ozet`, `operator_performans`, `user`

### Arama

#### Genel Arama
```http
GET http://bormeg.fun:3002/api/search?q=BRU001
```

#### Belirli Tabloda Arama
```http
GET http://bormeg.fun:3002/api/search?q=boru&table=urun_modelleri
```

#### Belirli Alanlarda Arama
```http
GET http://bormeg.fun:3002/api/search?q=PE&table=urun_modelleri&fields=stok_adi,stok_kodu
```

**Örnek Response:**
```json
{
  "success": true,
  "data": {
    "query": "boru",
    "results": [
      {
        "table": "urun_modelleri",
        "results": [
          {
            "id": 1,
            "stok_kodu": "BRU001",
            "stok_adi": "PE100 Boru 50mm"
          }
        ]
      }
    ],
    "totalResults": 1
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

---

## 📋 RESPONSE FORMATI

### Başarılı Response
```json
{
  "success": true,
  "data": {
    // Veri burada
  },
  "pagination": {  // Sayfalama varsa
    "page": 1,
    "limit": 50,
    "total": 1250,
    "totalPages": 25
  },
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

### Hata Response
```json
{
  "success": false,
  "error": "Hata mesajı",
  "details": "Detaylı hata açıklaması",
  "timestamp": "2024-01-27T10:30:00.000Z"
}
```

